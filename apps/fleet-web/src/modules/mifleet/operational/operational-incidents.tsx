import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  Box,
  Button,
  DataGrid,
  gridExpandedSortedRowEntriesSelector,
  IconButton,
  LinearProgress,
  Stack,
  styled,
  Tooltip,
  useDataGridColumnHelper,
  useGridApiRef,
  type DateRange,
  type GridColDef,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined'
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined'
import TrendingUpIcon from '@mui/icons-material/TrendingUp'
import type { DateTime } from 'luxon'
import { connect, type DispatchProp } from 'react-redux'
import { withRouter, type RouteComponentProps } from 'react-router'
import { useParams } from 'react-router-dom'

import type { MifleetReportReferredName } from 'api/types'
import { actions, miFleetOperationalSelectors } from 'duxs/mifleet/operational'
import { useModal } from 'src/hooks'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import {
  fetchDocumentArrayTypes,
  getArrayTypes,
} from 'src/modules/mifleet/DocumentsEdit/slice'
import { useDeleteDocumentMutation } from 'src/modules/mifleet/lite/api/useMiFleetCost'
import type { AppState } from 'src/root-reducer'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import type { FixMeAny } from 'src/types'

import { ctIntl, useMifleetFormattedNumber } from 'cartrack-ui-kit'
import AddNewCost from '../../mifleet/lite/import-data-new'
import {
  useCostsDateRangeFilter,
  useCostsFilters,
  type MiFleetCostsFiltersWithDateInNumber,
  type MifleetCostsFiltersWithDateTime,
} from '../api/costInput/shared/useCostsFilters'
import { DOCUMENT_CONCEPT_INCIDENT } from '../components/documents/concept-types'
import type { VehicleDetailsIncident } from '../lite/generalTypes'
import { DocumentStatusOptions, useDefaultRangeFilterInNumber } from '../lite/helper'
import { CustomPagination } from '../shared/footer-dataGrid'
import { SourceFromSourceId, StatusValidation } from '../shared/utils'
import { FloatingPanelCostDetailDrawer } from './floating-panel-detail'
import {
  exportCostsToXLSX,
  getExportFileNameArray,
  getInitialExtraLineArray,
} from './shared/utils'

const {
  isOperationallIncidentsLoading,
  allOperationalIncidents,
  allOperationalIncidentsTypes,
} = miFleetOperationalSelectors

const { fetchOperationalIncidents } = actions

type IncidentsProps = {
  isLoading: boolean
  automationId?: string
  operationalIncidents: Array<FixMeAny>
  operationalIncidentsTypes: Array<FixMeAny>
  incidentTypesDropDownOptions: Array<{ label: string; value: string }>
  viewReportClick: (key: MifleetReportReferredName) => void
} & ReturnType<typeof mapStateToProps> &
  DispatchProp &
  RouteComponentProps

const Incidents = (props: IncidentsProps) => {
  const costsFiltersOrLoading = useCostsFilters({
    stateKey: 'mifleetCostsIncidentsFilters',
  })

  if (costsFiltersOrLoading === 'loading') return null

  const { values: costsFilters, setCostsFilters } = costsFiltersOrLoading

  return (
    <IncidentsContent
      {...props}
      costsFilters={costsFilters}
      setCostsFilters={setCostsFilters}
    />
  )
}

const IncidentsContent = ({
  isLoading,
  automationId,
  operationalIncidents,
  operationalIncidentsTypes,
  incidentTypesDropDownOptions,
  viewReportClick,
  dispatch,
  arrayTypesAllDrivers,
  costsFilters,
  setCostsFilters,
}: IncidentsProps & {
  costsFilters: MiFleetCostsFiltersWithDateInNumber | null
  setCostsFilters: (values: MifleetCostsFiltersWithDateTime) => void
}) => {
  const columnHelper = useDataGridColumnHelper<VehicleDetailsIncident>({
    filterMode: 'client',
  })

  const [newCostActiveTab, setNewCostActiveTab] = useState<'add' | 'import'>('add')
  const [isAddCostModalOpen, addCostModal] = useModal<any>(false)
  const [isEditCostModalOpen, editCostModal] = useModal<any>(false)
  const [selectedRow, setSelectedRow] = useState<FixMeAny>(undefined)
  const { vehicleId } = useParams() as { vehicleId: FixMeAny }

  const formatNumber = useMifleetFormattedNumber()
  const shortcuts = useDateRangeShortcutItems()
  const deleteDocumentMutation = useDeleteDocumentMutation()
  const deleteDocumentMutate = deleteDocumentMutation.mutate

  const apiRef = useGridApiRef()

  const filterModel = useMemo(
    () => costsFilters?.filterModel ?? undefined,
    [costsFilters?.filterModel],
  )
  const defaultDateRangeFilterInNumber = useDefaultRangeFilterInNumber()
  const dateRangeFilter = useCostsDateRangeFilter(
    costsFilters?.dateRangeFilter ?? defaultDateRangeFilterInNumber,
  )

  useEffect(() => {
    if (!arrayTypesAllDrivers || arrayTypesAllDrivers.length === 0) {
      dispatch(fetchDocumentArrayTypes())
    }
  }, [arrayTypesAllDrivers, dispatch])

  useEffect(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
            vehicleId,
          }
        : { vehicleId }

    dispatch(fetchOperationalIncidents({ payload: dateObject }))
  }, [dateRangeFilter, dispatch, vehicleId])

  const getCostsList = useCallback(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
            vehicleId,
          }
        : { vehicleId }

    dispatch(fetchOperationalIncidents({ payload: dateObject }))
  }, [dateRangeFilter, dispatch, vehicleId])

  const columns = useMemo(
    (): Array<GridColDef<VehicleDetailsIncident>> => [
      columnHelper.dateTime({
        headerName: ctIntl.formatMessage({ id: 'Accident Date' }),
        field: 'incident_date',
        minWidth: 140,
        filterable: false,
        valueGetter: (_, row) => new Date(row.incident_date),
      }),
      columnHelper.string((_, row) => row.plate, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        field: 'plate',
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.singleSelect((_, row) => row.document_status_id.toString(), {
        headerName: ctIntl.formatMessage({ id: 'Document Status' }),
        field: 'document_status_id',
        valueOptions: DocumentStatusOptions(),
        renderCell: ({ row }) => (
          <StatusValidation
            statusId={row.document_status_id}
            statusName={row.document_status}
            source="costs"
          />
        ),
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.string(
        (_, row) =>
          ctIntl.formatMessage({
            id: SourceFromSourceId[row.source_id],
          }),
        {
          headerName: ctIntl.formatMessage({ id: 'Source' }),
          field: 'source_id',
          flex: 1,
        },
      ),
      columnHelper.singleSelect(
        (_, row) => {
          const type = operationalIncidentsTypes.find(
            (o: FixMeAny) => o.value === row.vehicle_incident_type_id,
          )
          return type ? type.name : ''
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Accident Type' }),
          field: 'vehicle_incident_type_id',
          flex: 1,
          minWidth: 150,
          valueOptions: incidentTypesDropDownOptions,
        },
      ),
      columnHelper.string(
        (_, row) => {
          const driver = arrayTypesAllDrivers.find(
            (o: FixMeAny) => o.driver_id === row.driver_id,
          )

          return driver ? driver.name : ''
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Driver Name' }),
          field: 'driver_id',
          flex: 1,
          minWidth: 100,
        },
      ),
      columnHelper.string((_, row) => row.description, {
        headerName: ctIntl.formatMessage({ id: 'Description' }),
        field: 'description',
        flex: 1,
        minWidth: 150,
      }),
      columnHelper.number((_, row) => Number(row.total_value), {
        headerName: ctIntl.formatMessage({ id: 'Gross Total' }),
        field: 'total_value',
        width: 150,
        valueFormatter: (_, row) => formatNumber(row.total_value),
        align: 'right',
        headerAlign: 'left',
      }),
      {
        headerName: ctIntl.formatMessage({ id: 'Actions' }),
        field: 'Transaction',
        type: 'actions',
        sortable: false,
        filterable: false,
        valueGetter: (_, row) => Number(row.document_id),
        renderCell: ({ row }) => (
          <Stack
            direction={'row'}
            gap={1}
          >
            <Tooltip
              title={ctIntl.formatMessage({ id: 'View Transaction' })}
              arrow
            >
              <IconButton
                id={automationId ? `view-doc-${automationId}-btn` : undefined}
                color="secondary"
                size="small"
                onClick={(e) => {
                  e.stopPropagation()
                  setSelectedRow(row)
                  editCostModal.open()
                }}
              >
                <RemoveRedEyeOutlinedIcon fontSize={'small'} />
              </IconButton>
            </Tooltip>
            <Tooltip
              title={ctIntl.formatMessage({
                id: 'Delete Cost',
              })}
              arrow
            >
              <IconButton
                onClick={() => {
                  deleteDocumentMutate(
                    { document_id: String(row.document_id) },
                    {
                      onSuccess: () => {
                        getCostsList()
                      },
                    },
                  )
                }}
                color="secondary"
                size="small"
              >
                <DeleteOutlineOutlinedIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Stack>
        ),
      },
    ],
    [
      columnHelper,
      incidentTypesDropDownOptions,
      operationalIncidentsTypes,
      arrayTypesAllDrivers,
      formatNumber,
      automationId,
      editCostModal,
      deleteDocumentMutate,
      getCostsList,
    ],
  )

  const updateCostsFilterState = (
    values:
      | { dateRangeFilter: MifleetCostsFiltersWithDateTime['dateRangeFilter'] }
      | { filterModel: MifleetCostsFiltersWithDateTime['filterModel'] },
  ) => {
    setCostsFilters({
      dateRangeFilter,
      filterModel: filterModel ?? null,
      ...values,
    })
  }

  const handleDataExport = async () => {
    const FilteredData = gridExpandedSortedRowEntriesSelector(apiRef)
    const array: Array<Record<string, FixMeAny>> = []
    for (const value of FilteredData) {
      array.push(value.model)
    }
    const data = await array.map((d) => {
      const {
        incident_date,
        plate,
        document_status,
        source_id,
        vehicle_incident_type_id,
        driver_id,
        description,
        total_value,
      } = d

      const type =
        operationalIncidentsTypes.find(
          (o: FixMeAny) => o.value === vehicle_incident_type_id,
        )?.name || ''

      const driver =
        arrayTypesAllDrivers.find((o: FixMeAny) => o.driver_id === driver_id)?.name ||
        ''

      return {
        incident_date: incident_date ? incident_date : '',
        plate,
        document_status: ctIntl.formatMessage({
          id: document_status,
        }),
        source_id: ctIntl.formatMessage({
          id: SourceFromSourceId[source_id],
        }),
        type,
        driver,
        description,
        total_value,
      }
    })

    const header = [
      'Accident Date',
      'Vehicle',
      'Document Status',
      'Source',
      'Accident Type',
      'Driver Name',
      'Description',
      'Gross Total',
    ]

    exportCostsToXLSX(header, data, {
      fileName: getExportFileNameArray('Accidents', dateRangeFilter),
      sheetName: 'Accidents',
      initialExtraLine: getInitialExtraLineArray(dateRangeFilter),
    })
  }

  return (
    <DataGridHolder>
      <UserDataGridWithSavedSettingsOnIDB
        apiRef={apiRef}
        Component={DataGrid}
        dataGridId="operationalAccidents"
        disableRowSelectionOnClick
        loading={!operationalIncidents || isLoading}
        pagination
        rows={operationalIncidents}
        getRowId={(row) => row.document_line_id}
        rowSelectionModel={selectedRow ? selectedRow.document_line_id : []}
        columns={columns}
        filterModel={filterModel}
        onRowClick={({ row }) => {
          setSelectedRow(row)
          editCostModal.open()
        }}
        onFilterModelChange={(newFilterModel) => {
          updateCostsFilterState({ filterModel: newFilterModel })
        }}
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          pagination: CustomPagination,
        }}
        slotProps={{
          filterPanel: {
            sx: {
              '.MuiNativeSelect-select': {
                paddingLeft: `${spacing[1]}`,
              },
            },
            columnsSort: 'asc',
          },
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: {
                show: true,
                props: {
                  defaultValue: filterModel?.quickFilterValues?.[0] ?? '',
                },
              },
              settingsButton: { show: true },
              filterButton: { show: true },
              dateRangePicker: {
                show: true,
                props: {
                  value: dateRangeFilter,
                  onChange: (newValue, ctx) => {
                    const dateValue: DateRange<DateTime> =
                      ctx?.shortcut?.label ===
                      ctIntl.formatMessage({
                        id: 'dateRangePicker.shortcutItem.reset',
                      })
                        ? [null, null]
                        : newValue

                    updateCostsFilterState({ dateRangeFilter: dateValue })
                  },
                  resetDefaultShortcut: shortcuts.last60Days,
                },
              },
            },
            extraContent: {
              right: (
                <Stack
                  spacing={1}
                  direction="row"
                >
                  <Button
                    color="inherit"
                    variant="outlined"
                    startIcon={<TrendingUpIcon />}
                    onClick={() =>
                      viewReportClick('REPORT_ACCIDENTS' as MifleetReportReferredName)
                    }
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Report',
                    })}
                  </Button>
                  <Button
                    color="inherit"
                    variant="outlined"
                    startIcon={<FileUploadOutlinedIcon />}
                    onClick={() => {
                      setNewCostActiveTab('import')
                      addCostModal.open()
                    }}
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Import',
                    })}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<FileDownloadOutlinedIcon />}
                    onClick={handleDataExport}
                    color="inherit"
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Export',
                    })}
                  </Button>
                  <Button
                    color="primary"
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={() => {
                      setNewCostActiveTab('add')
                      addCostModal.open()
                    }}
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Add Accidents Cost',
                    })}
                  </Button>
                </Stack>
              ),
            },
          }),
        }}
      />
      {isAddCostModalOpen && (
        <AddNewCost
          isSuccessCreation={() => {
            getCostsList()
            addCostModal.close()
          }}
          activeTab={newCostActiveTab}
          onClose={() => addCostModal.close()}
          forceMenu={{
            name: 'Accidents',
            id: DOCUMENT_CONCEPT_INCIDENT,
          }}
        />
      )}
      {selectedRow && isEditCostModalOpen && (
        <FloatingPanelCostDetailDrawer
          onClose={() => {
            setSelectedRow(undefined)
            editCostModal.close()
          }}
          detailsCost={selectedRow}
          forceMenu={{
            name: 'Accidents',
            id: DOCUMENT_CONCEPT_INCIDENT,
          }}
          isSuccessUpdating={() => {
            setSelectedRow(undefined)
            editCostModal.close()
            getCostsList()
          }}
        />
      )}
    </DataGridHolder>
  )
}

const mapStateToProps = (state: AppState) => ({
  arrayTypesAllDrivers: getArrayTypes(state).allDrivers,
  isLoading: isOperationallIncidentsLoading(state),
  operationalIncidents: allOperationalIncidents(state),
  operationalIncidentsTypes: allOperationalIncidentsTypes(state),
  incidentTypesDropDownOptions: [
    ...(allOperationalIncidentsTypes(state) as Array<FixMeAny>)
      .map((f: FixMeAny) => ({
        label: f.incident_type,
        value: f.incident_type,
      }))
      .sort((a, b) => a.label.localeCompare(b.label)),
  ],
})

export default withRouter(connect(mapStateToProps)(Incidents))

const DataGridHolder = styled(Box)({
  height: `calc(100% - 85px)`,
})
