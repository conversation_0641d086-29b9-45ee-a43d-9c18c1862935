import { split, trim } from 'lodash'
import type { DateRange } from '@karoo-ui/core'
import download from 'downloadjs'
import * as Excel from 'exceljs'
import type { DateTime } from 'luxon'

import type { FixMeAny } from 'src/types'

import { ctIntl } from 'cartrack-ui-kit'

const translateHeader = (o: string) => {
  const regex = new RegExp(/[0-9*]$/) // matches * and 0-9
  const exec = regex.exec(o)

  let header = o

  if (exec) {
    // If a header is for example "Address1", it removes the 1, translates 'Address' and adds the 1 back to the translated string
    header = `${ctIntl.formatMessage({ id: trim(o.replace(exec[0], '')) })}${exec[0]}`
  } else {
    header = ctIntl.formatMessage({ id: header })
  }
  return header
}

const exportCostsToXLSX = (
  headers: Array<string>,
  data: Array<FixMeAny>,
  options?: {
    fileName?: string
    sheetName?: string
    initialExtraLine?: {
      v: string
      s: {
        font?: { bold?: boolean; size?: number }
        alignment?: { vertical?: string; horizontal?: string }
        fill?: { fgColor?: { argb?: string }; type: string; pattern: string }
      }
    }
    fileFormat?: 'xlsx' | 'csv'
  },
) => {
  const {
    fileName = 'Costs',
    sheetName = 'Costs',
    initialExtraLine,
    fileFormat = 'xlsx',
  } = options ?? {}

  const sheetStyle = {
    font: { bold: true, size: 14 },
    alignment: { vertical: 'center', horizontal: 'center' },
    fill: {
      fgColor: {
        argb: 'ADD8E6',
      },
      type: 'pattern',
      pattern: 'solid',
    },
  }
  const translatedHeaders = headers.map((o: string) => translateHeader(o))

  const wb = new Excel.Workbook()
  const ws = wb.addWorksheet(ctIntl.formatMessage({ id: sheetName }))

  let first_Row
  if (initialExtraLine) {
    const lines = ws.addRows([[initialExtraLine.v], translatedHeaders])
    for (const item of lines) {
      ws.getRow(item.number).eachCell(function (cell) {
        ws.getCell(cell.address).style = initialExtraLine.s as FixMeAny
      })
    }
  } else {
    ws.addRow(translatedHeaders)
    ws.getRow(1).eachCell(function (cell) {
      ws.getCell(cell.address).style = sheetStyle as FixMeAny
    })
    first_Row = 2
  }

  ws.columns = translatedHeaders.map((cell, idx) => {
    const width =
      idx === 0 && initialExtraLine
        ? Number(initialExtraLine.v.length) + 10
        : Number(cell.length) + 10
    return {
      width,
    }
  })

  const newData = data.map((items) => Object.values(items).map((item) => item))
  ws.addRows(newData)

  //insert dropdown in cells
  if (!initialExtraLine && first_Row) {
    ws.getRow(first_Row).eachCell(function (cell: any) {
      if (typeof cell.value !== 'string') {
        const selectedItem = split(cell.value[0].replace('"', ''), ',')[0]
        ws.getCell(cell.address).dataValidation = {
          type: 'list',
          allowBlank: true,
          showErrorMessage: true,
          formulae: cell.value,
          error: ctIntl.formatMessage({
            id: 'Please select any value from dropdown',
          }),
        }
        ws.getCell(cell.address).value = selectedItem
      }
    })
  }

  if (fileFormat === 'xlsx') {
    wb.xlsx.writeBuffer().then(function (data) {
      const blob = new Blob([data])
      download(blob, `${fileName}.${fileFormat}`)
    })
  } else {
    wb.csv.writeBuffer().then(function (data) {
      const blob = new Blob([data])
      download(blob, `${fileName}.${fileFormat}`)
    })
  }
}

const getInitialExtraLineArray = (dateFilter?: DateRange<DateTime>) => ({
  v: `${ctIntl.formatMessage({ id: 'Date Range' })}: ${
    dateFilter && dateFilter[0] && dateFilter[1]
      ? dateFilter[0].toFormat('D') + ' -> ' + dateFilter[1].toFormat('D')
      : ctIntl.formatMessage({ id: 'all' })
  }`,
  s: {
    font: { bold: true, size: 14 },
    alignment: { vertical: 'center', horizontal: 'center' },
    fill: { fgColor: { argb: 'ADD8E6' }, type: 'pattern', pattern: 'solid' },
  },
})

const getExportFileNameArray = (name: string, dateFilter?: DateRange<DateTime>) =>
  `${ctIntl.formatMessage({ id: name })}-${
    dateFilter && dateFilter[0] && dateFilter[1]
      ? dateFilter[0].toFormat('ddMMyyyy') + '-' + dateFilter[1].toFormat('ddMMyyyy')
      : ctIntl.formatMessage({ id: 'all' })
  }`

export {
  exportCostsToXLSX,
  getExportFileNameArray,
  getInitialExtraLineArray,
  translateHeader,
}
