/* eslint-disable react-hooks/react-compiler */
import { useEffect, useMemo, useState } from 'react'
import {
  Box,
  Button,
  DataGrid,
  IconButton,
  LinearProgress,
  Stack,
  styled,
  useDataGridColumnHelper,
  useSearchTextField,
  type GridColDef,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import { DateTime } from 'luxon'
import { withRouter } from 'react-router'
import { isNonNullish } from 'remeda'
import { match, P } from 'ts-pattern'

import { actions as reducerActions } from 'duxs/mifleet/overview/overview'
import OverflowableTextTooltip from 'src/components/_popups/Tooltip/OverflowableText'
import {
  UserDataGridWithSavedSettingsOnIDB,
  UserFormattedLengthInKmOrMiles,
  useUserFormatLengthInKmOrMiles,
} from 'src/modules/components/connected'
import { fetchDocumentArrayTypes } from 'src/modules/mifleet/DocumentsEdit/slice'
import { exportCostsToXLSX } from 'src/modules/mifleet/operational/shared/utils'
import { CustomPagination } from 'src/modules/mifleet/shared/footer-dataGrid'
import MifleetContainer from 'src/modules/mifleet/shared/mifleetContainer'
import { useAppDispatch, useTypedSelector } from 'src/redux-hooks'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import {
  generateItemMatchesWithTextAndFilters,
  type Filters,
} from 'src/util-functions/search-utils'

import { ctIntl } from 'cartrack-ui-kit'
import DeleteSettingsDialog from '../../../shared/DeleteSettingsDialog'
import { StatusValidation } from '../../../shared/utils'
import { getStatusLabelReminder } from '../shared/helpers'
import AddReminderServiceModal from './AddReminderServiceModal'
import EditReminderServiceModal from './EditReminderServiceModal'

const distanceUnit = {
  km: 'Kilometers',
  mi: 'Miles',
  hrs: 'Hours',
}

const ReminderService = () => {
  const dispatch = useAppDispatch()
  const reminderList = useTypedSelector((state) => state.overview.reminderList)
  const [deletedServiceId, setDeletedServiceId] = useState<string | null>(null)

  const [serviceReminderModal, setServiceReminderModal] = useState<
    { type: 'create' } | { type: 'update'; serviceId: string } | null
  >(null)
  const searchProps = useSearchTextField('')

  const { formatLengthInKmOrMiles } = useUserFormatLengthInKmOrMiles()
  const columnHelper = useDataGridColumnHelper<ServiceReminder>({
    filterMode: 'client',
  })

  useEffect(() => {
    dispatch(reducerActions.fetchServicesList())
    dispatch(fetchDocumentArrayTypes())
    dispatch(reducerActions.fetchServiceTaskList())
  }, [dispatch])

  const lastCompletedColumnValue = (row: ServiceReminder) =>
    match([row.succeeded_expiration_date, row.succeeded_mileage_pediod])
      .with([P.nonNullable, P.nonNullable], ([date, period]) =>
        ctIntl.formatMessage(
          { id: 'mifleet.reminder.service.list.lastCompleted' },
          {
            values: {
              expirationDate: DateTime.fromSQL(date).toFormat('D'),
              mileagePeriod: period,
            },
          },
        ),
      )
      .with([P.nonNullable, P.nullish], ([date]) =>
        ctIntl.formatMessage(
          { id: 'mifleet.reminder.service.list.lastCompleted.part1' },
          { values: { expirationDate: DateTime.fromSQL(date).toFormat('D') } },
        ),
      )
      .with([P.nullish, P.nonNullable], ([_, period]) =>
        ctIntl.formatMessage(
          { id: 'mifleet.reminder.service.list.lastCompleted.part2' },
          { values: { mileagePeriod: period } },
        ),
      )
      .otherwise(() => null)

  const nextDueColumnValue = (row: ServiceReminder) =>
    match([row.odometer_diff, row.expiration_diff])
      .with([P.nonNullable, P.nonNullable], ([odometerDiff, expirationDiff]) =>
        ctIntl.formatMessage(
          { id: 'mifleet.reminder.service.list.nextDue' },
          { values: { odometerDiff, expirationDiff } },
        ),
      )
      .with([P.nonNullable, P.nullish], ([odometer]) => odometer)
      .with(
        [P.nullish, P.nonNullable],
        ([_, expiration]) => `${expiration} ${ctIntl.formatMessage({ id: 'Days' })}`,
      )
      .otherwise(() => null)

  const columns = useMemo(
    (): Array<GridColDef<ServiceReminder>> => [
      columnHelper.string((_, row) => row.plate, {
        headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
        field: 'plate',
        flex: 1,
        minWidth: 140,
      }),
      columnHelper.string(
        (_, row) =>
          `${row.service_type} ${row.service_date_interval} ${row.service_interval} ${row.service_interval_unit}`,
        {
          headerName: ctIntl.formatMessage({ id: 'Service Task' }),
          field: 'service_type',
          minWidth: 100,
          flex: 1.2,
          renderCell: ({ row }) => {
            const subTask = () => {
              if (
                isNonNullish(row.service_date_interval) &&
                isNonNullish(row.service_interval)
              ) {
                return (
                  <OverflowableTextTooltip>
                    {ctIntl.formatMessage(
                      { id: 'mifleet.reminder.service.list.subTask' },
                      {
                        values: {
                          dateInterval: row.service_date_interval
                            ?.toString()
                            .split(' ')[0],
                          dateIntervalUnit: row.service_date_interval
                            ?.toString()
                            .split(' ')[1]
                            ? ctIntl.formatMessage({
                                id: row.service_date_interval.toString().split(' ')[1],
                              })
                            : '',
                          serviceInterval: row.service_interval,
                          serviceIntervalUnit: row.service_interval_unit
                            ? ctIntl.formatMessage({
                                id: distanceUnit[
                                  row.service_interval_unit as keyof {
                                    km: string
                                    mi: string
                                    hrs: string
                                  }
                                ],
                              })
                            : '',
                        },
                      },
                    )}
                  </OverflowableTextTooltip>
                )
              } else if (isNonNullish(row.service_date_interval)) {
                return (
                  <>
                    {ctIntl.formatMessage({ id: 'Every' })}
                    <StyleService>
                      {row.service_date_interval?.toString().split(' ')[0]}
                    </StyleService>
                    {row.service_date_interval?.toString().split(' ')[1] &&
                      ctIntl.formatMessage({
                        id: row.service_date_interval?.toString().split(' ')[1],
                      })}
                  </>
                )
              } else if (isNonNullish(row.service_interval)) {
                return (
                  <OverflowableTextTooltip>
                    {`${row.service_interval} ${
                      row.service_interval_unit
                        ? ctIntl.formatMessage({
                            id: distanceUnit[
                              row.service_interval_unit as keyof typeof distanceUnit
                            ],
                          })
                        : ''
                    }
                  `}
                  </OverflowableTextTooltip>
                )
              }
              return
            }
            return (
              <TwoLines>
                {row.service_type}
                <SubText>{subTask()}</SubText>
              </TwoLines>
            )
          },
        },
      ),
      columnHelper.singleSelect(
        (_, row) => getStatusLabelReminder(row.status_level?.toString() || ''),
        {
          headerName: ctIntl.formatMessage({ id: 'Status' }),
          field: 'status_level',
          valueOptions: [
            {
              label: ctIntl.formatMessage({ id: 'Active' }),
              value: 'Active',
            },

            {
              label: ctIntl.formatMessage({ id: 'Due Now' }),
              value: 'Due Now',
            },
            {
              label: ctIntl.formatMessage({ id: 'Due Soon' }),
              value: 'Due Soon',
            },
            {
              label: ctIntl.formatMessage({ id: 'Overdue' }),
              value: 'Overdue',
            },
          ],
          minWidth: 100,
          renderCell: ({ row }) => (
            <StatusValidation
              statusId={String(row.status_level)}
              source={'service'}
            />
          ),
        },
      ),
      columnHelper.string(
        (_, row) => {
          const value = nextDueColumnValue(row)
          return isNonNullish(value) ? value.toString() : value
        },
        {
          headerName: ctIntl.formatMessage({ id: 'Next Due' }),
          field: 'odometer_diff',
          flex: 1,
          minWidth: 170,
          renderCell: ({ row }) => {
            const value = nextDueColumnValue(row)
            if (typeof value === 'number') {
              return (
                <UserFormattedLengthInKmOrMiles
                  valueInKm={value}
                  transformValueBeforeFormatting={Math.round}
                />
              )
            }
            return value ? (
              <OverflowableTextTooltip>{value}</OverflowableTextTooltip>
            ) : (
              value
            )
          },
        },
      ),
      columnHelper.string((_, row) => lastCompletedColumnValue(row), {
        headerName: ctIntl.formatMessage({ id: 'Last Completed' }),
        field: 'succeeded_expiration_date',
        flex: 1.7,
        minWidth: 180,
        renderCell: ({ row }) => {
          const value = lastCompletedColumnValue(row)
          return value && <OverflowableTextTooltip>{value}</OverflowableTextTooltip>
        },
      }),
      {
        field: 'complete',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Mark as Complete' }),
        width: 150,
        cellClassName: 'util-tableCenter',
        valueGetter: (_, row) => row.service_id,
        renderCell: (params) => (
          <IconButton
            onClick={() => {
              const serviceId = params.row.service_id
              if (serviceId) {
                dispatch(
                  reducerActions.updateServiceReminder({
                    data: {
                      ...params.row,
                      service_id: serviceId,
                      service_completed: true,
                    },
                  }),
                )
              }
            }}
            color="inherit"
          >
            <CheckIcon />
          </IconButton>
        ),
      },
      {
        field: 'cancel',
        type: 'actions',
        headerName: ctIntl.formatMessage({ id: 'Cancel' }),
        width: 80,
        cellClassName: 'util-tableCenter',
        valueGetter: (_, row) => row.service_id,
        renderCell: ({ row }) => (
          <IconButton
            onClick={(
              e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement, MouseEvent>,
            ) => {
              if (row.service_id) {
                e.preventDefault()
                e.stopPropagation()
                setDeletedServiceId(row.service_id)
              }
            }}
            color="inherit"
          >
            <CloseIcon />
          </IconButton>
        ),
      },
    ],
    [columnHelper, dispatch],
  )

  const makeTableData = useMemo(() => {
    const items: Array<ServiceReminder> = reminderList
    const searchFilters: Filters<ServiceReminder> = {
      search: [
        (u) => u.plate,
        (u) => u.service_date_interval?.toString(),
        (u) => u.service_interval?.toString(),
        (u) => u.service_interval_unit,
        (u) => getStatusLabelReminder(String(u.status_level?.toString())),
        (u) => u.service_type,
        (u) => u.odometer_diff?.toString(),
        (u) => u.expiration_diff?.toString(),
      ],
    }
    const { itemMatchesWithTextAndFilters } = generateItemMatchesWithTextAndFilters(
      searchProps.value,
    )
    return items.filter((category) =>
      itemMatchesWithTextAndFilters(category, searchFilters),
    )
  }, [searchProps.value, reminderList])

  const handleDataExport = async () => {
    const data = await makeTableData.map((d) => {
      const {
        plate,
        service_type,
        status_level,
        odometer_diff,
        expiration_diff,
        succeeded_expiration_date,
        succeeded_mileage_pediod,
      } = d

      const odometerDiff = () => {
        if (isNonNullish(odometer_diff) && isNonNullish(expiration_diff)) {
          return ctIntl.formatMessage(
            { id: 'mifleet.reminder.service.list.nextDue' },
            {
              values: {
                odometerDiff: odometer_diff,
                expirationDiff: expiration_diff,
              },
            },
          )
        } else if (isNonNullish(odometer_diff)) {
          return formatLengthInKmOrMiles({
            valueInKm: odometer_diff || 0,
          })
        } else if (isNonNullish(expiration_diff)) {
          return `${expiration_diff} ${ctIntl.formatMessage({ id: 'Days' })}`
        }
        return ''
      }

      const lastCompleted = () => {
        if (
          isNonNullish(succeeded_expiration_date) &&
          isNonNullish(succeeded_mileage_pediod)
        ) {
          return ctIntl.formatMessage(
            { id: 'mifleet.reminder.service.list.lastCompleted' },
            {
              values: {
                expirationDate: DateTime.fromSQL(succeeded_expiration_date).toFormat(
                  'D',
                ),
                mileagePeriod: succeeded_mileage_pediod,
              },
            },
          )
        } else if (isNonNullish(succeeded_expiration_date)) {
          return ctIntl.formatMessage(
            { id: 'mifleet.reminder.service.list.lastCompleted.part1' },
            {
              values: {
                expirationDate: DateTime.fromSQL(succeeded_expiration_date).toFormat(
                  'D',
                ),
              },
            },
          )
        } else if (isNonNullish(succeeded_mileage_pediod)) {
          return ctIntl.formatMessage(
            { id: 'mifleet.reminder.service.list.lastCompleted.part2' },
            { values: { mileagePeriod: succeeded_mileage_pediod } },
          )
        }
        return ''
      }

      return {
        plate,
        service_type,
        status: getStatusLabelReminder(String(status_level?.toString())),
        odometer_diff: odometerDiff(),
        lastCompleted: lastCompleted(),
      }
    })

    const header = ['Vehicle', 'Service Task', 'Status', 'Next Due', 'Last Completed']
    exportCostsToXLSX(header, data, {
      fileName: ctIntl.formatMessage({ id: 'Reminders' }),
      sheetName: 'Reminders',
    })
  }

  const rowSelectionModel = useMemo(
    () =>
      new Set(
        serviceReminderModal?.type === 'update' ? serviceReminderModal?.serviceId : [],
      ),
    [serviceReminderModal],
  )

  return (
    <MifleetContainer title="Service Reminders">
      <Box sx={{ height: '100%', pt: '12px' }}>
        <UserDataGridWithSavedSettingsOnIDB
          sx={{
            height: 'calc(100 - 82px)',
            '.MuiDataGrid-row .MuiDataGrid-cell': {
              span: {
                overflow: 'hidden !important',
                textOverflow: 'ellipsis !important',
                display: 'block',
              },
            },
          }}
          Component={DataGrid}
          dataGridId="serviceReminder"
          loading={!reminderList}
          pagination
          rows={makeTableData}
          getRowId={(row) => row.service_id}
          rowSelectionModel={rowSelectionModel}
          onRowClick={({ row }) => {
            if (isNonNullish(row) && row.service_id) {
              setServiceReminderModal({ type: 'update', serviceId: row.service_id })
            }
          }}
          columns={columns}
          slots={{
            toolbar: KarooToolbar,
            loadingOverlay: LinearProgress,
            pagination: CustomPagination,
          }}
          slotProps={{
            filterPanel: { columnsSort: 'asc' },
            toolbar: KarooToolbar.createProps({
              slots: {
                searchFilter: { show: true },
                settingsButton: { show: true },
                filterButton: { show: true },
              },
              extraContent: {
                right: (
                  <Stack
                    direction="row"
                    sx={{ gap: 1 }}
                  >
                    <Button
                      color="primary"
                      variant="outlined"
                      startIcon={<AddIcon />}
                      onClick={() => setServiceReminderModal({ type: 'create' })}
                      size="small"
                    >
                      {ctIntl.formatMessage({ id: 'Add Service Reminder' })}
                    </Button>
                    <Button
                      variant="outlined"
                      startIcon={<FileDownloadOutlinedIcon />}
                      onClick={handleDataExport}
                      color="inherit"
                      size="small"
                    >
                      {ctIntl.formatMessage({ id: 'Export' })}
                    </Button>
                  </Stack>
                ),
              },
            }),
          }}
        />
      </Box>
      {match(serviceReminderModal)
        .with({ type: 'create' }, () => (
          <AddReminderServiceModal
            onClose={() => {
              setServiceReminderModal(null)
            }}
          />
        ))
        .with({ type: 'update' }, ({ serviceId }) => (
          <EditReminderServiceModal
            serviceId={serviceId}
            onClose={() => {
              setServiceReminderModal(null)
            }}
          />
        ))
        .with(null, () => null)
        .exhaustive()}
      {deletedServiceId && (
        <DeleteSettingsDialog
          onClose={() => setDeletedServiceId(null)}
          onDelete={() => {
            if (deletedServiceId != null) {
              setDeletedServiceId(null)
              dispatch(
                reducerActions.deleteServiceReminder({ service_id: deletedServiceId }),
              )
            }
          }}
          labels={{ titleLabel: 'Service Reminders' }}
        />
      )}
    </MifleetContainer>
  )
}

export default withRouter(ReminderService)

const TwoLines = styled('span')({
  flexWrap: 'wrap',
})

const SubText = styled('span')({
  fontSize: '13px',
  color: '#a5a5a5',
})

const StyleService = styled('div')({
  margin: '0 3px',
  display: 'inline-block',
})
