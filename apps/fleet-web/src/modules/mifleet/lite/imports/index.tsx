import { useCallback, useEffect, useMemo, useState } from 'react'
import { camelCase, isEmpty, isNil } from 'lodash'
import {
  Autocomplete,
  Box,
  Button,
  Chip,
  CircularProgress,
  Stack,
  Step,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>per,
  TextField,
  Typography,
} from '@karoo-ui/core'
import ArrowRightAltOutlinedIcon from '@mui/icons-material/ArrowRightAltOutlined'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import RequestPageIcon from '@mui/icons-material/RequestPage'
import UploadFileIcon from '@mui/icons-material/UploadFile'
import WarningAmberOutlinedIcon from '@mui/icons-material/WarningAmberOutlined'
import { DateTime } from 'luxon'
import type { FileError, FileRejection, FileWithPath } from 'react-dropzone'
import { generatePath, useHistory } from 'react-router-dom'
import styled from 'styled-components'
import { match } from 'ts-pattern'
import XLSX from 'xlsx'

import { getAutocompleteVirtualizedProps } from 'src/components/_dropdowns/AutocompleteVirtualized'
import { useEffectEvent } from 'src/hooks/useEventHandler'
import { COSTS } from 'src/modules/app/components/routes'
import type {
  Sheet_to_json_result_data,
  Sheet_to_json_result_with_headers,
} from 'src/modules/importer/_ImporterComponents/types'
import { ImportTemplateData } from 'src/modules/importer/utils/mifleet-template-exporter'
import { exportCostsToXLSX } from 'src/modules/mifleet/operational/shared/utils'
import { useBackwardCompatibleDropzone } from 'src/util-components/useBackwardCompatibleDropzone'
import { showSupportedFileFormats } from 'src/util-functions/file-utils'
import {
  mifleetImportMimeObject,
  mifleetImportMimeString,
} from 'src/util-functions/mifleet/file-mime-types'

import { isTrue } from 'cartrack-utils'
import { ctIntl, ctToast } from 'cartrack-ui-kit'
import {
  useCreateMiFleetImport,
  useReadMiFleetImports,
  type MiFleetImportType,
} from '../api/useMiFleetImports'
import { ButtonsHolder } from '../capture-data/style'
import { fileToBase64, mapImportsToConceptMenuByMenuId } from '../capture-data/utils'
import { costCategory } from '../helper'

type Props = {
  defaultConcept: { name: string; id: string }
  onClose: () => void
}

type Views = 'upload' | 'match'

const FILE_UPLOAD_MAX_SIZE = 5000000
const FILE_NAME_MAX_SIZE = 65
const steps = ['Upload File', 'Map Columns']

const ImportsDash = ({ defaultConcept, onClose }: Props) => {
  const [selectedImport, setSelectedImport] = useState<MiFleetImportType | null>(null)

  const [activeView, setActiveView] = useState<Views>('upload')
  const [fileData, setFileData] = useState<{
    file: FileWithPath | null
    headers: Array<string> | null
    data: Sheet_to_json_result_data | null
  } | null>(null)
  const [activeStep, setActiveStep] = useState(0)
  const importComponents = match(activeView)
    .with('upload', () => (
      <ImportsUpload
        defaultConcept={defaultConcept}
        setActiveView={setActiveView}
        setActiveFile={(file, headers, data) => setFileData({ file, headers, data })}
        setMainImport={(importSelected: MiFleetImportType) =>
          setSelectedImport(importSelected)
        }
        setActiveStep={setActiveStep}
        onClose={onClose}
      />
    ))
    .with('match', () => (
      <ImportsMatch
        fileData={fileData}
        activeImport={selectedImport}
        setActiveView={setActiveView}
        setActiveStep={setActiveStep}
        onClose={onClose}
      />
    ))
    .exhaustive()
  return (
    <>
      {selectedImport && !isTrue(selectedImport.is_quickimport) && (
        <Box sx={{ width: '350px', m: '0 24px 40px' }}>
          <Stepper
            activeStep={activeStep}
            nonLinear={false}
            sx={{
              '.MuiStepIcon-root.Mui-completed': {
                color: '#4CAF50 ',
              },
            }}
          >
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel> {ctIntl.formatMessage({ id: label })}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>
      )}
      {importComponents}
    </>
  )
}

type MatchProps = {
  fileData: {
    file: FileWithPath | null
    headers: Array<string> | null
    data: Sheet_to_json_result_data | null
  } | null
  activeImport: MiFleetImportType | null
  setActiveView: (view: Views) => void
  setActiveStep: (step: number) => void
  onClose?: () => void
}

const ImportsMatch = ({
  fileData,
  activeImport,
  setActiveView,
  setActiveStep,
  onClose,
}: MatchProps) => {
  const history = useHistory()
  const createImport = useCreateMiFleetImport()

  const [localMapping, setLocalMapping] = useState(
    JSON.parse(JSON.stringify(activeImport?.mapping)),
  )

  const requiredFields = activeImport?.mapping.filter((i) => i.required) || []

  const requiredFieldsMappedCount = localMapping.filter(
    (i: MiFleetImportType['mapping'][0]) =>
      Object.hasOwn(i, 'match_column_index') &&
      i.required &&
      !isNil(i.match_column_index),
  ).length

  const fieldsMappingMatch = requiredFieldsMappedCount === requiredFields.length

  const requiredFieldsMapped = localMapping.filter(
    (i: MiFleetImportType['mapping'][0]) =>
      i.required &&
      (!Object.hasOwn(i, 'match_column_index') || isNil(i.match_column_index)),
  )

  const memoFileHeaders = useMemo(
    () =>
      fileData?.headers?.map((header: string, idx: number) => ({
        label: header,
        value: String(idx),
      })),
    [fileData?.headers],
  )
  useEffect(() => {
    setActiveStep(1)
  }, [setActiveStep])

  const onActiveImportChange = useEffectEvent(() => {
    const rawMapping = [...localMapping]
    // eslint-disable-next-line
    // eslint-disable-next-line unicorn/no-array-for-each
    activeImport?.mapping.forEach((_, idx) => {
      if (fileData?.headers && fileData?.headers[idx]) {
        rawMapping[idx].match_column_index = idx + 1
      }
    })
    setLocalMapping(rawMapping)
  })
  useEffect(() => {
    onActiveImportChange()
  }, [activeImport])

  const generateMatchingFields = (
    field: MiFleetImportType['mapping'][0],
    indexOnArray: number,
  ) => (
    <MatchingDropdownWrapper key={field.def}>
      <FieldSection>
        <Typography
          sx={{
            width: '100%',
          }}
        >
          {ctIntl.formatMessage({
            id: `mifleet.imports.mapping.match.field.${field.def}`,
          })}
          {field.required && (
            <Typography
              component={'span'}
              sx={{
                color: 'red',
              }}
            >
              *
            </Typography>
          )}
        </Typography>
        <ArrowWrapper>
          <ArrowRightAltOutlinedIcon sx={{ width: '24px' }} />
        </ArrowWrapper>
      </FieldSection>

      <FieldSection>
        <Autocomplete
          sx={{ width: '100%' }}
          {...getAutocompleteVirtualizedProps({
            options: memoFileHeaders as Readonly<
              Array<{ label: string; value: string }>
            >,
          })}
          value={
            fileData?.headers?.length
              ? {
                  label:
                    fileData?.headers[
                      localMapping[indexOnArray].match_column_index - 1
                    ] || '',
                  value: (localMapping[indexOnArray].match_column_index - 1).toString(),
                }
              : null
          }
          onChange={(_, newValue) => {
            const rawMapping = [...localMapping]
            if (isEmpty(newValue)) {
              delete rawMapping[indexOnArray].match_column_index
            } else {
              rawMapping[indexOnArray].match_column_index = Number(newValue?.value) + 1
            }

            setLocalMapping(rawMapping)
          }}
          renderInput={(params) => <TextField {...params} />}
        />
      </FieldSection>
      <FieldSection>
        <Typography
          sx={{
            width: '100%',
          }}
        >
          {fileData?.data?.map((i, index) => {
            const currentValue = i[localMapping[indexOnArray].match_column_index - 1]
            if (index <= 1 && currentValue) {
              return (
                <span key={index}>
                  {currentValue instanceof Date
                    ? DateTime.fromJSDate(currentValue).toFormat('D')
                    : currentValue}
                  {index === 0 && fileData?.data && fileData.data.length > 1 && (
                    <span>, </span>
                  )}
                  {index !== 0 && <span>... </span>}
                </span>
              )
            }
            return
          })}
        </Typography>
      </FieldSection>
    </MatchingDropdownWrapper>
  )

  const finishMatchImport = async () => {
    const base64 = await fileToBase64(fileData?.file as FileWithPath)

    const data = {
      originalFileName: fileData?.file?.name,
      mifleet_import_type: activeImport?.mifleet_import_type,
      mapping: localMapping,
      base64: base64 as string,
    }

    createImport.mutate(data, {
      onSuccess(_data) {
        onClose?.()
        history.push(generatePath(COSTS.LITE_IMPORT_DATA_HISTORY.path))
      },
    })
  }

  return (
    <>
      <MainContainer>
        {fileData?.file && (
          <Typography variant="subtitle2">
            {ctIntl.formatMessage(
              {
                id: 'importing',
              },
              {
                values: {
                  fileName: fileData?.file?.name,
                },
              },
            )}
          </Typography>
        )}
        {requiredFieldsMapped.length > 0 && (
          <Stack
            direction="row"
            sx={{ color: '#663C00', mt: 2, display: 'flex', alignItems: 'center' }}
            flexWrap="wrap"
          >
            <WarningAmberOutlinedIcon
              color="warning"
              sx={{ marginRight: '12px' }}
            />
            {(requiredFieldsMapped as Array<{ def: string }>).map((i, index) => (
              <Box key={index}>
                {ctIntl.formatMessage({
                  id: `mifleet.imports.mapping.match.field.${i.def}`,
                })}
                {index < requiredFieldsMapped.length - 1 && (
                  <span style={{ marginRight: '5px' }}>{','}</span>
                )}
              </Box>
            ))}
            <span style={{ marginLeft: '5px' }}>
              {ctIntl
                .formatMessage({
                  id:
                    requiredFieldsMapped.length > 1
                      ? `mifleet.imports.mapping.required.fields`
                      : 'is required',
                })
                .toLowerCase()}
            </span>
          </Stack>
        )}
        <Container>
          <StaticTableHeaders>
            <Typography sx={{ color: '#00000099', width: '33%' }}>
              {ctIntl.formatMessage({ id: 'MiFleet Fields' })}
            </Typography>
            <Typography sx={{ color: '#00000099', width: '33%' }}>
              {ctIntl.formatMessage({ id: 'File Column Headers' })}
            </Typography>
            <Typography sx={{ color: '#00000099', width: '33%' }}>
              {ctIntl.formatMessage({ id: 'Field values' })}
            </Typography>
          </StaticTableHeaders>
          <MatchingDropdownsContainer>
            {fileData?.headers?.length &&
              activeImport?.mapping.length &&
              activeImport?.mapping.map((field, idx: number) =>
                generateMatchingFields(field, idx),
              )}
          </MatchingDropdownsContainer>
        </Container>
      </MainContainer>
      <ButtonsHolder>
        <Stack
          direction="row"
          justifyContent={'space-between'}
          sx={{ m: 3 }}
        >
          <Button
            size="medium"
            color="secondary"
            variant="outlined"
            onClick={() => setActiveView('upload')}
          >
            {ctIntl.formatMessage({ id: 'Cancel' })}
          </Button>
          <Button
            size="medium"
            color="primary"
            disabled={!fieldsMappingMatch || createImport.isPending}
            onClick={finishMatchImport}
            variant="contained"
          >
            {ctIntl.formatMessage({ id: 'Next' })}
          </Button>
        </Stack>
      </ButtonsHolder>
    </>
  )
}

type UploadsProps = {
  defaultConcept: { name: string; id: string }
  setActiveView: (view: Views) => void
  setActiveFile: (
    file: FileWithPath | null,
    headers: Array<string> | null,
    data: Sheet_to_json_result_data | null,
  ) => void
  setMainImport: (importSelected: MiFleetImportType) => void
  setActiveStep: (step: number) => void
  onClose: () => void
}

const ImportsUpload = ({
  defaultConcept,
  setActiveView,
  setActiveFile,
  setMainImport,
  setActiveStep,
  onClose,
}: UploadsProps) => {
  const history = useHistory()
  const { data: allImports = [], isPending } = useReadMiFleetImports()
  const createImport = useCreateMiFleetImport()

  const [selectedCategory, setSelectedCategory] =
    useState<Record<string, any>>(defaultConcept)
  const [selectedImport, setSelectedImport] = useState(
    mapImportsToConceptMenuByMenuId(selectedCategory.id),
  )

  const [importedFile, setImportedFile] = useState<FileWithPath | null>(null)
  const [headersData, setHeadersData] = useState<Array<string> | null>(null)
  const [importedFileData, setImportedFileData] =
    useState<Sheet_to_json_result_data | null>(null)
  useEffect(() => {
    setActiveStep(0)
  }, [setActiveStep])

  useEffect(() => {
    setSelectedImport(mapImportsToConceptMenuByMenuId(selectedCategory.id))
  }, [selectedCategory.id])

  useEffect(() => {
    if (!selectedCategory.name) setSelectedCategory(costCategory[0])

    // eslint-disable-next-line react-hooks/react-compiler, react-hooks/exhaustive-deps
  }, [])

  const memoCostCategory = costCategory.map((c) => ({ label: c.name, value: c.id }))
  const memoImports = useMemo(
    () =>
      allImports.filter(
        (o) =>
          o.import_category_type ===
          selectedCategory.name.replaceAll(' ', '').toLowerCase(),
      ),
    [allImports, selectedCategory.name],
  )

  useEffect(() => {
    const importS = memoImports.find(
      (i) => i.mifleet_import_type === selectedImport,
    ) as MiFleetImportType

    setMainImport(importS)
  }, [memoImports, selectedImport, setMainImport])
  //const importIsDefault = selectedImport.includes('GLOBAL')

  const onDrop = useCallback(async (acceptedFiles: Array<FileWithPath>) => {
    for (const file of acceptedFiles) {
      const arrayBuffer = await file.arrayBuffer()
      const workbook = XLSX.read(arrayBuffer, {
        type: 'array',
        cellDates: true,
      })
      for (const sheet of workbook.SheetNames) {
        const headersFoundInFile = XLSX.utils.sheet_to_json(workbook.Sheets[sheet], {
          header: 1,
          defval: '',
        }) as Sheet_to_json_result_with_headers

        const [headers, ...data] = headersFoundInFile
        setImportedFileData(data)
        setHeadersData(headers)
      }
      setImportedFile(file)
    }
  }, [])

  const nameLengthValidator = (file: File) => {
    if (file?.name.length >= FILE_NAME_MAX_SIZE) {
      return {
        code: 'name-too-large',
        message: `documents.fileUpload.name.large`,
      }
    }
    return null
  }

  const dropRejectedFiles = (rejectedFiles: Array<FileRejection>) => {
    rejectedFiles.map(({ file, errors }: FileRejection) => {
      const fileName = file.name

      const invalidName = errors?.find(
        ({ code }: FileError) => code === 'name-too-large',
      )

      let errorMessage = 'documents.fileUpload.not.supported'

      if (invalidName) {
        errorMessage = invalidName.message
      }

      if (file.size === 0) {
        errorMessage = 'documents.fileUpload.empty'
      }

      if (file.size > FILE_UPLOAD_MAX_SIZE) {
        errorMessage = 'documents.fileUpload.large'
      }

      return ctToast.fire(
        'error',
        ctIntl.formatMessage({ id: errorMessage }, { values: { fileName } }),
      )
    })
  }

  const { getRootProps, getInputProps } = useBackwardCompatibleDropzone({
    validator: nameLengthValidator,
    onDropRejected: dropRejectedFiles,
    accept: mifleetImportMimeString,
    onDrop,
    noClick: Boolean(importedFile),
    noDrag: Boolean(importedFile),
    noKeyboard: Boolean(importedFile),
  })

  const renderWarningSection = (
    <Box mb={3}>
      <Typography sx={{ fontWeight: '500', mb: 2 }}>
        1.
        {ctIntl.formatMessage({
          id: 'mifleet.imports.prepare.file',
        })}
      </Typography>
      <Typography>
        {ctIntl.formatMessage({
          id: 'mifleet.default.imports.warnings.newRule1',
        })}
      </Typography>
      <Typography>
        {ctIntl.formatMessage({ id: 'mifleet.default.imports.warnings.newRule2' })}
      </Typography>
    </Box>
  )

  const importFile = async () => {
    if (!importedFile) {
      return
    }

    const importS = memoImports.find(
      (i) => i.mifleet_import_type === selectedImport,
    ) as MiFleetImportType

    if (isTrue(importS.is_quickimport)) {
      const base64 = await fileToBase64(importedFile as FileWithPath)

      const data = {
        originalFileName: importedFile.name,
        mifleet_import_type: importS.mifleet_import_type,
        mapping: undefined,
        base64: base64 as string,
      }

      createImport.mutate(data, {
        onSuccess(_data) {
          onClose?.()
          history.push(generatePath(COSTS.LITE_IMPORT_DATA_HISTORY.path))
        },
      })
    } else {
      setActiveFile(importedFile, headersData, importedFileData)
      setActiveView('match')
    }
  }

  const promptsMappingFields = useMemo(
    () => memoImports.find((i) => i.mifleet_import_type === selectedImport)?.mapping,

    [memoImports, selectedImport],
  )
  if (isPending) {
    return (
      <LoadingContainer>
        <CircularProgress />
      </LoadingContainer>
    )
  }

  return (
    <>
      <MainContainer>
        {renderWarningSection}
        <Stack
          direction="row"
          mb={2}
          justifyContent="space-between"
        >
          {!defaultConcept.name && (
            <Autocomplete
              sx={{ width: 368 }}
              {...getAutocompleteVirtualizedProps({
                options: memoCostCategory,
              })}
              onChange={(_, newValue) =>
                setSelectedCategory({ name: newValue.label, id: newValue.value })
              }
              value={memoCostCategory.find((i) => i.value === selectedCategory.id)}
              disableClearable
              renderInput={(params) => (
                <TextField
                  {...params}
                  label={ctIntl.formatMessage({
                    id: 'Cost Category',
                  })}
                />
              )}
            />
          )}
          <Autocomplete
            sx={{ width: 368 }}
            {...getAutocompleteVirtualizedProps({
              options: memoImports,
            })}
            onChange={(_, newValue) => {
              setSelectedImport(
                newValue?.mifleet_import_type || mapImportsToConceptMenuByMenuId('5'),
              )
            }}
            value={
              (memoImports.find(
                (i) => i.mifleet_import_type === selectedImport,
              ) as MiFleetImportType) || null
            }
            disableClearable
            renderInput={(params) => (
              <TextField
                {...params}
                label={ctIntl.formatMessage({ id: 'Import Type' })}
              />
            )}
          />
        </Stack>
        {promptsMappingFields && (
          <ColumnBox>
            <InfoOutlinedIcon
              color="info"
              sx={{ mr: 1 }}
            />
            <Stack direction="column">
              <Typography sx={{ fontWeight: '500', mb: 1 }}>
                {ctIntl.formatMessage({ id: 'mifleet.imports.container.file.header' })}
              </Typography>
              <Stack
                direction="row"
                flexWrap="wrap"
              >
                {promptsMappingFields.map((item) => (
                  <Chip
                    key={`${item.def}_${item.match_column_index}`}
                    variant="outlined"
                    sx={({ palette }) => ({
                      border: 'none',
                      borderRight: `1px solid ${palette.action.focus}`,
                      borderRadius: '0',
                      height: 'auto',
                      mb: 2,
                      mr: '11px',
                      '.MuiChip-label': {
                        pl: 0,
                      },
                    })}
                    label={
                      <>
                        {ctIntl.formatMessage({
                          id: `mifleet.imports.mapping.match.field.${item.def}`,
                        })}
                        {item.required && <span style={{ color: 'red' }}>*</span>}
                      </>
                    }
                  />
                ))}
              </Stack>
              <Stack
                direction="row"
                mt={1}
                justifyContent="space-between"
                alignItems="center"
              >
                <Box sx={{ fontSize: '13px' }}>
                  {ctIntl.formatMessage({ id: `Required Fields` })}
                  <span style={{ color: 'red' }}>*</span>
                </Box>
                <Button
                  variant="text"
                  color="info"
                  size="small"
                  startIcon={<FileDownloadOutlinedIcon />}
                  onClick={() =>
                    exportCostsToXLSX(
                      ImportTemplateData[
                        selectedImport as keyof typeof ImportTemplateData
                      ].headers,
                      ImportTemplateData[
                        selectedImport as keyof typeof ImportTemplateData
                      ].data,
                      {
                        fileName: camelCase(
                          ctIntl.formatMessage({
                            id: `${selectedImport}-template`,
                          }),
                        ),
                      },
                    )
                  }
                >
                  {ctIntl.formatMessage({ id: 'Download Template' })}
                </Button>
              </Stack>
            </Stack>
          </ColumnBox>
        )}

        <Typography sx={{ fontWeight: '500', mt: 4, mb: 2 }}>
          2.
          {ctIntl.formatMessage({
            id: 'mifleet.imports.file.upload',
          })}
        </Typography>
        <DropzoneWrapper {...getRootProps()}>
          <DropzoneContainer>
            <input {...getInputProps()} />
            <DropzonePlaceholder>
              {importedFile ? (
                <DropzoneTextContent>
                  <RequestPageIcon
                    fontSize={'large'}
                    sx={{ color: 'action/active' }}
                  />
                  <Typography sx={{ fontWeight: '400', mb: 2, mt: 2, color: 'black' }}>
                    {importedFile?.name}
                  </Typography>
                  <Button
                    size="small"
                    color="error"
                    variant="outlined"
                    onClick={() => {
                      setImportedFile(null)
                    }}
                  >
                    {ctIntl.formatMessage({ id: 'Remove file' })}
                  </Button>
                </DropzoneTextContent>
              ) : (
                <DropzoneTextContent>
                  <UploadFileIcon
                    fontSize="large"
                    sx={{ color: 'action/active' }}
                  />
                  <Typography sx={{ fontWeight: '400', mb: 2, mt: 2, color: 'black' }}>
                    {ctIntl.formatMessage({
                      id: 'mifleet.imports.file.drop.title',
                    })}
                  </Typography>
                  <Button
                    size="medium"
                    color="primary"
                    variant="outlined"
                    sx={{ mb: 2 }}
                  >
                    {ctIntl.formatMessage({
                      id: 'help.support.dragAndDropFiles.browseFiles',
                    })}
                  </Button>
                  <Typography
                    variant="caption"
                    color="text.secondary"
                  >
                    {ctIntl.formatMessage(
                      { id: 'global.upload.supportedFormats' },
                      {
                        values: {
                          formats: showSupportedFileFormats(
                            Object.keys(mifleetImportMimeObject),
                          ),
                        },
                      },
                    )}
                  </Typography>
                </DropzoneTextContent>
              )}
            </DropzonePlaceholder>
          </DropzoneContainer>
        </DropzoneWrapper>
      </MainContainer>
      <ButtonsHolder>
        <Stack
          direction="row"
          justifyContent={'space-between'}
          sx={{ m: 3 }}
        >
          <Button
            size="medium"
            color="secondary"
            variant="outlined"
            onClick={onClose}
          >
            {ctIntl.formatMessage({ id: 'Cancel' })}
          </Button>
          <Button
            size="medium"
            color="primary"
            onClick={importFile}
            disabled={!importedFile || createImport.isPending}
            variant="contained"
          >
            {ctIntl.formatMessage({ id: 'Next' })}
          </Button>
        </Stack>
      </ButtonsHolder>
    </>
  )
}

export default ImportsDash

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
`
const MainContainer = styled.div`
  margin: 0 24px;
  padding-bottom: 24px;
`
/* UPLOAD */
const ColumnBox = styled.div`
  padding: 16px 16px 8px;
  border: 1px solid #2196f3;
  border-radius: 4px;
  display: flex;
  flex-direction: row;
`
const DropzoneWrapper = styled.section`
  display: grid;
  background-color: #fff;
  box-shadow: 0px 0px 0px 1px #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  min-height: 350px;
  padding: 10px;
`
const DropzoneContainer = styled.div`
  height: 100%;
  outline: none;
`
const DropzonePlaceholder = styled.div`
  align-items: center;
  color: #666;
  display: flex;
  flex: 1;
  justify-content: center;
  height: 100%;
`

const DropzoneTextContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`

/* UPLOAD */

/* MATCH */

const Container = styled.section`
  display: flex;
  flex-direction: column;
  height: 100%;
  align-items: center;
  margin-top: 24px;
`

const StaticTableHeaders = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
`

const MatchingDropdownsContainer = styled.div`
  width: 100%;
  height: 560px;
  overflow-y: auto;
`

const MatchingDropdownWrapper = styled.div`
  display: flex;
  width: 100%;
  padding-top: 16px;
  align-items: center;
`

const FieldSection = styled.div`
  display: flex;
  justify-content: space-between;
  width: 33%;
  max-width: 50%;
  padding: 0 10px;
`
const ArrowWrapper = styled.div`
  min-width: 50px;
`
/* MATCH */
