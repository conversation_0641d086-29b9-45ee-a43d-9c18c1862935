import { useCallback, useEffect, useMemo, useState } from 'react'
import {
  Box,
  Button,
  DataGrid,
  gridExpandedSortedRowEntriesSelector,
  LinearProgress,
  styled,
  useGridApiRef,
  type DateRange,
  type GridColDef,
} from '@karoo-ui/core'
import AddIcon from '@mui/icons-material/Add'
import FileDownloadIcon from '@mui/icons-material/FileDownload'
import FileUploadOutlinedIcon from '@mui/icons-material/FileUploadOutlined'
import type { DateTime } from 'luxon'

import { actions, miFleetOperationalSelectors } from 'duxs/mifleet/operational'
import { useModal } from 'src/hooks'
import { useDateRangeShortcutItems } from 'src/hooks/useDateRangeShortcutItems'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import {
  exportCostsToXLSX,
  getExportFileNameArray,
  getInitialExtraLineArray,
} from 'src/modules/mifleet/operational/shared/utils'
import { useAppDispatch, useTypedSelector } from 'src/redux-hooks'
import { spacing } from 'src/shared/components/styled/global-styles/spacing'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import type { FixMeAny } from 'src/types'

import { ctIntl, Stats, useMifleetFormattedNumber } from 'cartrack-ui-kit'
import {
  useCostsDateRangeFilter,
  useCostsFilters,
  type MiFleetCostsFiltersWithDateInNumber,
  type MifleetCostsFiltersWithDateTime,
} from '../api/costInput/shared/useCostsFilters'
import { CustomPagination } from '../shared/footer-dataGrid'
import { useDefaultRangeFilterInNumber } from './helper'
import AddNewCost from './import-data-new'

const { getOperationalLoading, allOperationalDashboard } = miFleetOperationalSelectors

const { fetchOperationalDashboard } = actions

const OverviewCosts = () => {
  const costsFiltersOrLoading = useCostsFilters({
    stateKey: 'mifleetCostsOverviewFilters',
  })

  if (costsFiltersOrLoading === 'loading') return null

  const { values: costsFilters, setCostsFilters } = costsFiltersOrLoading

  return (
    <OverviewCostsContent
      costsFilters={costsFilters}
      setCostsFilters={setCostsFilters}
    />
  )
}

const OverviewCostsContent = ({
  costsFilters,
  setCostsFilters,
}: {
  costsFilters: MiFleetCostsFiltersWithDateInNumber | null
  setCostsFilters: (values: MifleetCostsFiltersWithDateTime) => void
}) => {
  const dispatch = useAppDispatch()

  const isLoading = useTypedSelector(getOperationalLoading)
  const operationalDashboard = useTypedSelector(allOperationalDashboard)

  const formatNumber = useMifleetFormattedNumber()
  const apiRef = useGridApiRef()
  const shortcuts = useDateRangeShortcutItems()
  const [newCostActiveTab, setNewCostActiveTab] = useState<'add' | 'import'>('add')
  const [isAddCostModalOpen, addCostModal] = useModal<any>(false)

  const filterModel = useMemo(
    () => costsFilters?.filterModel ?? undefined,
    [costsFilters?.filterModel],
  )
  const defaultDateRangeFilterInNumber = useDefaultRangeFilterInNumber()
  const dateRangeFilter = useCostsDateRangeFilter(
    costsFilters?.dateRangeFilter ?? defaultDateRangeFilterInNumber,
  )

  const getDashboardList = useCallback(() => {
    const dateObject =
      dateRangeFilter && dateRangeFilter[0] && dateRangeFilter[1]
        ? {
            start_date: dateRangeFilter[0].toFormat('yyyy-MM-dd'),
            end_date: dateRangeFilter[1].toFormat('yyyy-MM-dd'),
          }
        : null

    dispatch(fetchOperationalDashboard({ payload: dateObject }))
  }, [dateRangeFilter, dispatch])

  useEffect(() => {
    getDashboardList()
  }, [getDashboardList])

  const updateCostsFilterState = (
    values:
      | { dateRangeFilter: MifleetCostsFiltersWithDateTime['dateRangeFilter'] }
      | { filterModel: MifleetCostsFiltersWithDateTime['filterModel'] },
  ) => {
    setCostsFilters({
      dateRangeFilter,
      filterModel: filterModel ?? null,
      ...values,
    })
  }

  const vehicleCell = () =>
    ({
      headerName: ctIntl.formatMessage({ id: 'Vehicle' }),
      minWidth: 150,
      field: 'plate',
      headerAlign: 'center',
      align: 'center',
      valueGetter: (_, row) => row.plate,
    }) satisfies GridColDef<OperationalDashboard>

  const columns = useMemo((): Array<GridColDef<OperationalDashboard>> => {
    const totalCell = () =>
      ({
        headerName: 'Total',
        minWidth: 150,
        field: 'total',
        headerAlign: 'center',
        align: 'center',
        valueGetter: (_, row) => row.total,
        renderCell: ({ row }) => formatNumber(row.total),
      }) satisfies GridColDef<OperationalDashboard>

    const createConceptHeader = (
      title: string,
      filterKey: keyof OperationalDashboard,
    ): GridColDef<OperationalDashboard> => ({
      headerName: ctIntl.formatMessage({ id: title }),
      field: filterKey,
      minWidth: 100,
      flex: 1,
      headerAlign: 'center',
      align: 'center',
      valueGetter: (_, row) => (row[filterKey] ? formatNumber(row[filterKey]) : '0'),
    })

    return [
      vehicleCell(),
      createConceptHeader('Fuel', 'fuel_total'),
      createConceptHeader('Tolls', 'toll_total'),
      createConceptHeader('Fines', 'fine_total'),
      createConceptHeader('Tyres', 'tyre_total'),
      createConceptHeader('Maintenance', 'maintenance_total'),
      createConceptHeader('Accidents', 'accident_total'),
      createConceptHeader('Insurance', 'insurance_total'),
      createConceptHeader('Accessories', 'accessory_total'),
      createConceptHeader('Breakdowns', 'breakdown_total'),
      createConceptHeader('Cleanings', 'cleaning_total'),
      createConceptHeader('Consumables', 'consumable_total'),
      createConceptHeader('Leasings', 'leasing_total'),
      createConceptHeader('Oils', 'oil_total'),
      createConceptHeader('Permits', 'permit_total'),
      createConceptHeader('Purchases', 'purchase_total'),
      createConceptHeader('Rentals', 'rental_total'),
      createConceptHeader('Taxes', 'tax_total'),
      createConceptHeader('Maintenance Contract', 'contract_maintenance_total'),
      createConceptHeader('Insurance Contract', 'contract_insurance_total'),
      createConceptHeader('Fuel Card Contract', 'contract_fuel_card_total'),
      createConceptHeader('Finance Contract', 'contract_financing_total'),
      totalCell(),
    ]
  }, [formatNumber])

  const handleDataExport = async () => {
    const FilteredData = gridExpandedSortedRowEntriesSelector(apiRef)
    const array: Array<Record<string, FixMeAny>> = []
    for (const value of FilteredData) {
      array.push(value.model)
    }
    const data = await array.map((d) => {
      const {
        plate: vehicle,
        fuel_total: fuel,
        toll_total: tolls,
        fine_total: fines,
        tyre_total: tyres,
        maintenance_total: maintenance,
        accident_total: accidentes,
        insurance_total: insurance,
        accessory_total: accessories,
        breakdown_total: breakdowns,
        cleaning_total: cleanings,
        consumable_total: consumables,
        leasing_total: leasings,
        oil_total: oils,
        permit_total: permits,
        purchase_total: purchases,
        rental_total: rentals,
        tax_total: taxes,
        contract_maintenance_total: crtMaintenance,
        contract_insurance_total: crtInsurance,
        contract_fuel_card_total: crtFuelCard,
        contract_financing_total: crtFinancing,
        total,
      } = d
      return {
        vehicle,
        fuel,
        tolls,
        fines,
        tyres,
        maintenance,
        accidentes,
        insurance,
        accessories,
        breakdowns,
        cleanings,
        consumables,
        leasings,
        oils,
        permits,
        purchases,
        rentals,
        taxes,
        crtMaintenance,
        crtInsurance,
        crtFuelCard,
        crtFinancing,
        total,
      }
    })

    const header = [
      'Vehicle',
      'Fuel',
      'Tolls',
      'Fines',
      'Tyres',
      'Maintenance',
      'Accidents',
      'Insurance',
      'Accessories',
      'Breakdowns',
      'Cleanings',
      'Consumables',
      'Leasings',
      'Oils',
      'Licenses',
      'Purchases',
      'Rentals',
      'Taxes',
      'Maintenance Contract',
      'Insurance Contract',
      'Fuel Card Contract',
      'Finance Contract',
      'Total',
    ]

    exportCostsToXLSX(header, data, {
      fileName: getExportFileNameArray('FleetCostsOverview', dateRangeFilter),
      sheetName: 'FleetCostsOverview',
      initialExtraLine: getInitialExtraLineArray(dateRangeFilter),
    })
  }

  const defaultVisibility = {
    accessory_total: false,
    breakdown_total: false,
    cleaning_total: false,
    consumable_total: false,
    leasing_total: false,
    oil_total: false,
    permit_total: false,
    purchase_total: false,
    rental_total: false,
    tax_total: false,
    contract_maintenance_total: false,
    contract_insurance_total: false,
    contract_fuel_card_total: false,
    contract_financing_total: false,
  }

  return (
    <DataGridHolder>
      <UserDataGridWithSavedSettingsOnIDB
        apiRef={apiRef}
        Component={DataGrid}
        dataGridId="operationalDashboard"
        disableRowSelectionOnClick
        loading={!operationalDashboard || isLoading}
        pagination
        rows={operationalDashboard}
        getRowId={(row) => row.vehicle_id}
        columns={columns}
        filterModel={filterModel}
        onFilterModelChange={(newFilterModel) =>
          updateCostsFilterState({ filterModel: newFilterModel })
        }
        slots={{
          toolbar: KarooToolbar,
          loadingOverlay: LinearProgress,
          pagination: CustomPagination,
        }}
        slotProps={{
          filterPanel: {
            sx: {
              '.MuiNativeSelect-select': {
                paddingLeft: `${spacing[1]}`,
              },
            },
            columnsSort: 'asc',
          },
          toolbar: KarooToolbar.createProps({
            slots: {
              searchFilter: {
                show: true,
                props: {
                  defaultValue: filterModel?.quickFilterValues?.[0] ?? '',
                },
              },
              settingsButton: { show: true },
              filterButton: { show: true },
              dateRangePicker: {
                show: true,
                props: {
                  value: dateRangeFilter,
                  onChange: (newValue, ctx) => {
                    const dateValue: DateRange<DateTime> =
                      ctx?.shortcut?.label ===
                      ctIntl.formatMessage({
                        id: 'dateRangePicker.shortcutItem.reset',
                      })
                        ? [null, null]
                        : newValue

                    updateCostsFilterState({ dateRangeFilter: dateValue })
                  },
                  resetDefaultShortcut: shortcuts.last60Days,
                },
              },
            },
            extraContent: {
              right: (
                <>
                  <Stats
                    data={[
                      {
                        key: 'MiFleet Vehicles',
                        value: operationalDashboard.length,
                      },
                    ]}
                  />
                  <Button
                    color="inherit"
                    variant="outlined"
                    startIcon={<FileUploadOutlinedIcon />}
                    onClick={() => {
                      setNewCostActiveTab('import')
                      addCostModal.open()
                    }}
                    size="small"
                  >
                    {ctIntl.formatMessage({
                      id: 'Import',
                    })}
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<FileDownloadIcon />}
                    onClick={handleDataExport}
                    color="inherit"
                    size="small"
                  >
                    {ctIntl.formatMessage({ id: 'Export' })}
                  </Button>
                  <Button
                    color="primary"
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={() => {
                      setNewCostActiveTab('add')
                      addCostModal.open()
                    }}
                    size="small"
                  >
                    {ctIntl.formatMessage({ id: 'Add Cost' })}
                  </Button>
                </>
              ),
            },
          }),
        }}
        initialState={{
          columns: {
            columnVisibilityModel: defaultVisibility,
          },
        }}
      />
      {isAddCostModalOpen && (
        <AddNewCost
          isSuccessCreation={() => {
            getDashboardList()
            addCostModal.close()
          }}
          activeTab={newCostActiveTab}
          onClose={() => addCostModal.close()}
          forceMenu={{
            name: 'Multi Cost',
            id: 'multicost',
          }}
        />
      )}
    </DataGridHolder>
  )
}

type OperationalDashboard = {
  vehicle_id: string
  manufacturer: string
  model: string
  total: string | number
  plate: string
  accident_total?: string
  fine_total?: string
  fuel_total?: string
  insurance_total?: string
  maintenance_total?: string
  toll_total?: string
  tyre_total?: string
  accessory_total?: string
  breakdown_total?: string
  cleaning_total?: string
  consumable_total?: string
  leasing_total?: string
  oil_total?: string
  permit_total?: string
  purchase_total?: string
  rental_total?: string
  tax_total?: string
  contract_maintenance_total?: string
  contract_insurance_total?: string
  contract_fuel_card_total?: string
  contract_financing_total?: string
}

export default OverviewCosts

export const StyledTextCell = styled(Box)`
  :hover {
    text-decoration: underline;
  }

  a {
    color: #333333;
  }

  font-size: 14px;
`

const DataGridHolder = styled(Box)({
  height: `calc(100% - 85px)`,
})
