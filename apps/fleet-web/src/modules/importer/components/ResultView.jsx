import { Component } from 'react'
import { concat, uniqueId, zipWith } from 'lodash'
import { any, arrayOf, bool, func, shape, string } from 'prop-types'
import styled from 'styled-components'

import { Alert, ctIntl, Spinner, Table } from 'cartrack-ui-kit'
import isValidMFPrompts from '../utils/mifleet-prompt-validation'
import removeEmptyLines from '../utils/remove-empty-import'
import translateMiFleetImportError from '../utils/translate-mifleet-import-error'

class ResultView extends Component {
  static propTypes = {
    onMount: func.isRequired,
    result: arrayOf(shape({})),
    importData: arrayOf(arrayOf(string)).isRequired,
    headers: arrayOf(string).isRequired,
    clearImportResult: func.isRequired,
    isMiFleet: bool,
    importError: any,
    isSelectedMenu: bool,
    onRestart: func,
  }

  static defaultProps = {
    result: null,
    importError: null,
  }
  //just will affect on Mifleet
  componentDidUpdate(prevProps) {
    if (
      Boolean(this.props.isSelectedMenu) &&
      Boolean(this.props.isSelectedMenu) !== Boolean(prevProps.isSelectedMenu)
    ) {
      this.props.onRestart()
    }
  }
  componentDidMount() {
    this.props.onMount(this.props.result)
  }

  componentWillUnmount() {
    this.props.clearImportResult()
  }

  render() {
    const { result, importData, isMiFleet, importError } = this.props

    const cleanImportData = removeEmptyLines(importData)

    let importedData = isMiFleet ? cleanImportData : importData

    if (result) {
      const specificResult = isMiFleet ? result.objectList.importData : result
      let resultMessage = specificResult
      let status = []
      let withStatusFilteredData = []

      if (isMiFleet) {
        importedData = cleanImportData.map((d) => Object.values(d))
        // check if it has the new format type
        if (specificResult && specificResult.length > 0) {
          withStatusFilteredData = specificResult.filter((r) => {
            if (r.import_status !== undefined) return r
          })

          if (withStatusFilteredData.length > 0) {
            resultMessage = specificResult.map(
              (message) => message.error_message || 'Success',
            )
            status = specificResult.map((status) => status.import_status)
          }
        }
      }
      // check if it has the new format type
      else if (specificResult && specificResult.length > 0) {
        withStatusFilteredData = specificResult.filter((r) => {
          if (r.message && r.status !== undefined) return r
        })

        if (withStatusFilteredData.length > 0) {
          resultMessage = specificResult.map((message) => message.message)
          status = specificResult.map((status) => status.status)
        }
      }

      const data = zipWith(resultMessage, importedData, status, concat)

      const getTrProp = (_state, rowInfo) => {
        if (rowInfo) {
          let backgroundColor = 'none'
          const getLastIndex = rowInfo.original.length - 1
          const responseStatus = rowInfo.original[getLastIndex]
          if (withStatusFilteredData.length > 0) {
            if (!responseStatus || responseStatus === 'error') {
              backgroundColor = 'rgba(206, 82, 57, 0.5)'
            } else if (responseStatus || responseStatus === 'success') {
              backgroundColor = 'rgba(92, 174, 96, 0.5)'
            }
          }

          return {
            style: {
              backgroundColor: backgroundColor,
              marginBottom: '2px',
            },
          }
        }
        return {}
      }

      const columns = [
        {
          Header: ctIntl.formatMessage({ id: 'Result' }),
          accessor: '0', // 1st element in data
          minWidth: 300,
          className:
            specificResult &&
            specificResult.length > 0 &&
            withStatusFilteredData.length === 0
              ? 'Table-focusedColumn'
              : '',
          Cell: ({ value }) => (
            <Result>
              {value ? (
                value.split(',').map((str) => {
                  let updatedValue = str

                  if (isMiFleet) {
                    updatedValue = translateMiFleetImportError(str)
                  }

                  return <div key={str}>- {updatedValue}</div>
                })
              ) : (
                <div key={uniqueId()}>
                  -{' '}
                  {ctIntl.formatMessage({
                    id: 'Something went wrong, please try again',
                  })}
                </div>
              )}
            </Result>
          ),
        },
        /*
    Header are being received as "Document Type*" and that allows us to check if it is required or not but does not match the "Document Type" key
    in the translations file and that leads to it not being translated.
    The solution was to remove the *, translate what is left and then add the * again
  */

        ...this.props.headers
          .filter((item) => item.split(':')[1] !== 'XXX')
          .map((h, i) => ({
            Header:
              isMiFleet && isValidMFPrompts(h)
                ? `${ctIntl.formatMessage({
                    id: h.split(':')[0].replace('*', ''),
                  })}${h.split(':')[0].includes('*') ? '*' : ''}`
                : h,
            accessor: String(i + 1),
          })),
        {
          Header: 'status',
          show: false,
        },
      ]

      return (
        <StyledTableResult
          columns={columns}
          data={data}
          resizable
          getTrProps={getTrProp}
        />
      )
    }

    if (importError && isMiFleet) {
      return (
        <Alert
          show={importError}
          message={ctIntl.formatMessage({
            id: 'This file is not valid. Please check your file.',
          })}
        />
      )
    }

    return <Spinner />
  }
}

export default ResultView

const Result = styled.div`
  display: flex;
  flex-direction: column;
`

const StyledTableResult = styled(Table)`
  margin-top: 43px;

  .Table-options {
    top: -30px;
    left: 33px;
  }
`
