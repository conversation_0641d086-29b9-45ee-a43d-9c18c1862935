export const FleetImportTemplates = {
  DRIVER: [
    {
      '1': '<PERSON>',
      '2': 'Doe',
      '3': '999999999',
      '4': '<EMAIL>',
      '5': '123123123',
      '6': 'male',
      '7': '784521',
      '8': 'SG',
      '9': '77',
      '10': 'test only',
      '11': '01/01/2020',
      '12': '02/02/2020',
      '13': '03/03/2020',
      '14': '54545D',
      '15': 'Corolla Car',
      '16': '01/01/2020',
    },
  ],
  DRIVER_GROUP: [
    {
      '1': 'John',
      '2': 'Doe',
      '3': '999999999',
      '4': '<EMAIL>',
      '5': '123123123',
      '6': 'male',
      '7': '784521',
      '8': 'SG',
      '9': '77',
      '10': 'test only',
      '11': '01/01/2020',
      '12': '02/02/2020',
      '13': '03/03/2020',
      '14': '54545D',
      '15': 'Corolla <PERSON>',
      '16': '01/01/2020',
      '17': 'driver group1',
    },
  ],
  GEOFENCES: [
    {
      '1': 'Eiffel Tower',
      '2': 'Landmark in Paris',
      '3': 'Circle',
      '4': '1000',
      '5': '48.8584',
      '6': '2.2945',
    },
  ],
  GEOFENCES_GROUP: [
    {
      '1': 'Eiffel Tower Sq',
      '2': 'Landmark in Paris',
      '3': 'Square',
      '4': '1000',
      '5': '48.8584',
      '6': '2.2945',
      '7': 'Paris',
      '8': 'Cool Places in Paris',
    },
  ],
  GEOFENCES_POLYGON: [
    {
      '1': 'Around Eiffel Tower',
      '2': 'Landmark in Paris',
      '3': '48.858667#2.293774#48.858554#2.295802#48.857580#2.294064#48.858371#2.292894',
      '4': 'Paris',
      '5': 'Cool Places in Paris',
    },
  ],
  VEHICLES: [
    {
      '1': 'Vehicle_Registration',
      '2': 'Heavy Trucks',
    },
  ],
  POI: [
    {
      '1': 'Eiffel Tower',
      '2': 'Landmark in Paris',
      '3': '48.8584',
      '4': '2.2945',
    },
  ],
  RUC: [
    {
      '1': 'CTT_73-ZT-71',
      '2': '543123',
      '3': '721666347',
      '4': '2/10841NZ',
      '5': '2023-03-29',
      '6': '1000',
      '7': '10000',
      '8': 'Class 1',
      '9': '100',
    },
  ],
  VEHICLE_SCDF_TFMS_LOGBOOK: [
    {
      '1': 'M433R876R',
      '2': 'Test Driver Name',
      '3': 'vehicle commander',
      '4': '721666347',
      '5': 'Purpose',
      '6': '2025-09-22',
      '7': '10:00',
      '8': '2025-09-23',
      '9': '11:00',
    },
  ],
}
