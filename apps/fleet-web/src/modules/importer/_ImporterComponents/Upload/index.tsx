import { useCallback, useEffect, useMemo, useState } from 'react'
import { startCase } from 'lodash'
import {
  <PERSON>ert,
  AlertTitle,
  Box,
  Button,
  Chip,
  CircularProgressDelayedAbsolute,
  FormControl,
  InputLabel,
  MenuItem,
  styled as MuiStyled,
  Select,
  Stack,
  type SelectChangeEvent,
} from '@karoo-ui/core'
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined'
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile'
import UploadFileIcon from '@mui/icons-material/UploadFile'
import {
  useDropzone,
  type Accept,
  type FileError,
  type FileRejection,
  type FileWithPath,
} from 'react-dropzone'
import { match } from 'ts-pattern'
import type { Except } from 'type-fest'
import XLSX from 'xlsx'

import { fileToBase64 } from 'src/modules/mifleet/lite/capture-data/utils'
import { exportCostsToXLSX } from 'src/modules/mifleet/operational/shared/utils'
import IntlTypography from 'src/util-components/IntlTypography'
import { showSupportedFileFormats } from 'src/util-functions/file-utils'

import { ctIntl, ctToast } from 'cartrack-ui-kit'
import type { ImporterDrawerTab } from '../../schema'
import type { ComplexLocalImportType, ImportType } from '../../types'
import useImporterListQuery from '../../useImporterListQuery'
import { removeLastAsterisk } from '../../utils'
import type { Sheet_to_json_result_with_headers } from '../types'
import { FleetImportTemplates } from './FleetImportsTemplates'

const FILE_UPLOAD_MAX_SIZE = 5000000
const FILE_NAME_MAX_SIZE = 65

type Props = {
  activeTab?: ImporterDrawerTab
  onFileReceived: (fileData: ComplexLocalImportType | null) => void
  data: NonNullable<ReturnType<typeof useImporterListQuery>['data']>
}

const importFileTypes: Accept = {
  // NOTE: do not support xlsx and xls since they could not handle the date and time correctly
  // 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  // 'application/vnd.ms-excel': ['.xls'],
  // 'application/vnd.oasis.opendocument.spreadsheet': ['.ods'], // To be supported?
  'text/csv': ['.csv'],
}

const ImportUploadFileViewContent = ({ onFileReceived, activeTab, data }: Props) => {
  const [selectedImport, setSelectedImport] = useState<ImportType | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [selectedSubCategory, setSelectedSubCategory] = useState<string>('')
  const [importedFile, setImportedFile] = useState<FileWithPath | null>(null)

  const categoriesStructure = useMemo(() => {
    const categories = {} as Record<
      ImportType['import_category_type'],
      { category: ImportType; subCategories: Array<ImportType> }
    >

    for (const item of data) {
      if (!categories[item.import_category_type]) {
        categories[item.import_category_type] = {
          category: item,
          subCategories: [],
        }
      } else {
        categories[item.import_category_type].subCategories.push(item)
      }
    }
    return Object.values(categories)
  }, [data])

  const hasSubCategories = useMemo(() => {
    if (selectedCategory) {
      const possibleSubCategories = categoriesStructure.find(
        (item) => item.category.import_id === selectedCategory,
      )
      if (possibleSubCategories && possibleSubCategories.subCategories.length > 0) {
        return [possibleSubCategories.category, ...possibleSubCategories.subCategories]
      }
    }
    return false
  }, [categoriesStructure, selectedCategory])

  useEffect(() => {
    if (activeTab) {
      const selectedCategory = categoriesStructure.find(
        (item) => item.category.import_category_type === activeTab,
      )
      if (selectedCategory) {
        setSelectedCategory(selectedCategory.category.import_id)
        setSelectedSubCategory(selectedCategory.category.import_id)
        setSelectedImport(selectedCategory.category)
      }
    }
  }, [activeTab, categoriesStructure])

  const nameLengthValidator = (file: File) => {
    if (file?.name.length >= FILE_NAME_MAX_SIZE) {
      return {
        code: 'name-too-large',
        message: `documents.fileUpload.name.large`,
      }
    }
    return null
  }

  const dropRejectedFiles = (rejectedFiles: Array<FileRejection>) => {
    rejectedFiles.map(({ file, errors }: FileRejection) => {
      const fileName = file.name

      const invalidName = errors?.find(
        ({ code }: FileError) => code === 'name-too-large',
      )

      let errorMessage = 'documents.fileUpload.not.supported'

      if (invalidName) {
        errorMessage = invalidName.message
      }

      if (file.size === 0) {
        errorMessage = 'documents.fileUpload.empty'
      }

      if (file.size > FILE_UPLOAD_MAX_SIZE) {
        errorMessage = 'documents.fileUpload.large'
      }

      return ctToast.fire(
        'error',
        ctIntl.formatMessage({ id: errorMessage }, { values: { fileName } }),
      )
    })
  }

  const handleCategoryChange = (e: SelectChangeEvent) => {
    setSelectedCategory(e.target.value)
    const filteredImport = categoriesStructure.find(
      (i) => i.category.import_id === e.target.value,
    )
    if (filteredImport) {
      setSelectedImport(filteredImport.category)
      if (filteredImport.subCategories.length > 0) {
        setSelectedSubCategory(e.target.value)
      }
    }
  }

  const handleSubCategoryChange = (e: SelectChangeEvent) => {
    setSelectedSubCategory(e.target.value)
    const filteredImport = data.find((i) => i.import_id === e.target.value)
    if (filteredImport) {
      setSelectedImport(filteredImport)
    }
  }

  const onDrop = useCallback(
    async (acceptedFiles: Array<FileWithPath>) => {
      for (const file of acceptedFiles) {
        const arrayBuffer = await file.arrayBuffer()
        const workbook = XLSX.read(arrayBuffer, {
          type: 'array',
          cellDates: true,
          dateNF: 'dd/mm/yyyy', // NOTE: This is important for date formatting in XLSX files
        })
        for (const sheet of workbook.SheetNames) {
          const headersFoundInFile = XLSX.utils.sheet_to_json(workbook.Sheets[sheet], {
            header: 1,
            defval: '',
          }) as Sheet_to_json_result_with_headers

          const [headers, ...data] = headersFoundInFile

          const base64 = await fileToBase64(file)
          onFileReceived({
            fileBase64: base64 as string,
            fileName: file.name,
            headers,
            data,
            import: selectedImport as ImportType,
          })
        }

        setImportedFile(file)
      }
    },
    [onFileReceived, selectedImport],
  )

  const isDropzoneDisabled = !activeTab && !selectedImport

  const { getRootProps, getInputProps } = useDropzone({
    validator: nameLengthValidator,
    onDropRejected: dropRejectedFiles,
    accept: importFileTypes,
    onDrop,
    noClick: isDropzoneDisabled,
    noDrag: isDropzoneDisabled,
    noKeyboard: isDropzoneDisabled,
  })

  return (
    <Stack
      gap={2}
      sx={{ pt: 2, height: '100%' }}
    >
      <Stack gap={1}>
        <AlertTitle>
          1.{ctIntl.formatMessage({ id: 'mifleet.imports.prepare.file' })}
        </AlertTitle>
        <IntlTypography
          msgProps={{ id: 'mifleet.default.imports.warnings.newRule1' }}
        />
        <StyledGrid>
          <FormControl
            fullWidth
            size="small"
            disabled={Boolean(activeTab)}
          >
            <InputLabel id="import-category">
              {ctIntl.formatMessage({ id: 'Category' })}
            </InputLabel>
            <Select
              labelId="import-category"
              id="import-category"
              value={selectedCategory || ''}
              label={ctIntl.formatMessage({ id: 'Category' })}
              onChange={handleCategoryChange}
            >
              {categoriesStructure.map(({ category }) => (
                <MenuItem
                  key={category.import_id}
                  value={category.import_id}
                >
                  {startCase(category.import_description)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {hasSubCategories && (
            <FormControl
              fullWidth
              size="small"
            >
              <InputLabel id="import-type">
                {ctIntl.formatMessage({ id: 'Import Type' })}
              </InputLabel>
              <Select
                labelId="import-type"
                id="import-type"
                value={selectedSubCategory || ''}
                label={ctIntl.formatMessage({ id: 'Import Type' })}
                onChange={handleSubCategoryChange}
              >
                {hasSubCategories.map((importer) => (
                  <MenuItem
                    key={importer.import_id}
                    value={importer.import_id}
                  >
                    {importer.import_description}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
        </StyledGrid>
        <IntlTypography
          msgProps={{ id: 'mifleet.default.imports.warnings.newRule2' }}
          sx={{ opacity: 0.6 }}
        />
      </Stack>
      {selectedImport && (
        <>
          <Alert
            variant="outlined"
            severity="info"
          >
            <AlertTitle sx={{ display: 'flex', justifyContent: 'space-between' }}>
              {ctIntl.formatMessage({ id: 'mifleet.imports.container.file.header' })}

              <Chip
                variant="outlined"
                sx={({ typography }) => ({
                  border: 'none',
                  fontWeight: typography.fontWeightRegular,
                })}
                label={
                  <>
                    {ctIntl.formatMessage({ id: 'Required Fields' })}
                    <span style={{ color: 'red' }}>*</span>
                  </>
                }
              />
            </AlertTitle>
            <Stack
              direction="row"
              flexWrap="wrap"
            >
              {selectedImport.prompt_map.map((item) => (
                <Chip
                  key={item.name}
                  variant="outlined"
                  sx={({ palette }) => ({
                    color: 'info/main',
                    border: 'none',
                    borderRight: `1px solid ${palette.action.focus}`,
                    borderRadius: '0',
                    height: 'auto',
                    mb: 1,
                    mr: '11px',
                    '.MuiChip-label': {
                      pl: 0,
                    },
                  })}
                  label={
                    <>
                      {ctIntl.formatMessage({
                        // Remove the asterisk from the column name if present, as we will add and style it later.
                        // This is also necessary for translation.
                        id: removeLastAsterisk(item.name),
                      })}
                      {item.required && <span style={{ color: 'red' }}>*</span>}
                    </>
                  }
                />
              ))}
            </Stack>
          </Alert>
          <Stack
            direction="row"
            gap={1}
            alignItems="center"
          >
            <IntlTypography msgProps={{ id: 'mifleet.imports.template.description' }} />
            <Button
              variant="outlined"
              color="info"
              size="small"
              startIcon={<FileDownloadOutlinedIcon />}
              onClick={() => {
                exportCostsToXLSX(
                  selectedImport.prompt_map.map(
                    (h) => `${removeLastAsterisk(h.name)}${h.required ? '*' : ''}`,
                  ),
                  FleetImportTemplates[selectedImport.import_identifier],
                  {
                    fileName: ctIntl.formatMessage({
                      id: selectedImport.import_description,
                    }),
                    fileFormat: 'csv',
                  },
                )
              }}
            >
              {ctIntl.formatMessage({ id: 'Download Template' })}
            </Button>
          </Stack>
        </>
      )}
      <AlertTitle>
        2. {ctIntl.formatMessage({ id: 'mifleet.imports.file.upload' })}
      </AlertTitle>
      <DropzoneWrapper
        isDisabled={isDropzoneDisabled}
        {...getRootProps()}
      >
        <DropzoneContainer>
          <input {...getInputProps()} />
          <DropzonePlaceholder>
            {importedFile ? (
              <DropzoneTextContent>
                <InsertDriveFileIcon
                  fontSize="large"
                  sx={{ color: 'action/active' }}
                />
                <IntlTypography msgProps={{ id: importedFile?.name }} />
                <Button
                  size="small"
                  color="error"
                  variant="outlined"
                  onClick={() => {
                    onFileReceived(null)
                    setImportedFile(null)
                  }}
                >
                  {ctIntl.formatMessage({ id: 'Remove file' })}
                </Button>
              </DropzoneTextContent>
            ) : (
              <DropzoneTextContent>
                <UploadFileIcon
                  fontSize="large"
                  sx={{ color: 'action/active' }}
                />
                <IntlTypography msgProps={{ id: 'mifleet.imports.file.drop.title' }} />
                <Button
                  size="medium"
                  color="primary"
                  variant="outlined"
                  disabled={isDropzoneDisabled}
                >
                  {ctIntl.formatMessage({
                    id: 'help.support.dragAndDropFiles.browseFiles',
                  })}
                </Button>
                <IntlTypography
                  variant="caption"
                  color="text.secondary"
                  msgProps={{
                    id: 'global.upload.supportedFormats',
                    values: {
                      formats: showSupportedFileFormats(['csv']),
                    },
                  }}
                />
              </DropzoneTextContent>
            )}
          </DropzonePlaceholder>
        </DropzoneContainer>
      </DropzoneWrapper>
    </Stack>
  )
}

const ImportUploadFileView = ({ onFileReceived, activeTab }: Except<Props, 'data'>) => {
  const listQuery = useImporterListQuery()

  return match(listQuery)
    .with({ status: 'error' }, () => null)
    .with({ status: 'pending' }, () => <CircularProgressDelayedAbsolute />)
    .with({ status: 'success' }, ({ data }) => (
      <ImportUploadFileViewContent
        onFileReceived={onFileReceived}
        activeTab={activeTab}
        data={data}
      />
    ))
    .exhaustive()
}

export default ImportUploadFileView

const StyledGrid = MuiStyled(Box)(({ theme }) =>
  theme.unstable_sx({
    display: 'grid',
    gridTemplateColumns: '1fr 1fr',
    gap: 2,
  }),
)

const DropzoneWrapper = MuiStyled(Box, {
  shouldForwardProp: (prop) => prop !== 'isDisabled',
})<{ isDisabled: boolean }>(({ theme, isDisabled }) =>
  theme.unstable_sx({
    display: 'grid',
    backgroundColor: '#fff',
    boxShadow: '0px 0px 0px 1px #e0e0e0',
    borderRadius: '4px',
    cursor: isDisabled ? 'not-allowed' : 'pointer',
    height: '100%',
    py: 11,
  }),
)

const DropzoneContainer = MuiStyled(Box)({
  height: '100%',
  outline: 'none',
})

const DropzonePlaceholder = MuiStyled(Stack)({
  flexDirection: 'row',
  alignItems: 'center',
  color: '#666',
  flex: '1',
  justifyContent: 'center',
  height: '100%',
})

const DropzoneTextContent = MuiStyled(Stack)(({ theme }) =>
  theme.unstable_sx({
    alignItems: 'center',
    gap: 2,
  }),
)
