import { Component, Fragment } from 'react'
import { camelCase, compact, concat, find, has, isEmpty, isNil, zipWith } from 'lodash'
import { arrayOf, bool, func, shape, string } from 'prop-types'
import { connect } from 'react-redux'
import { withRouter } from 'react-router'
import styled from 'styled-components'

import {
  clearImportResult,
  fetchImporters,
  getCategories,
  getFileFieldsValid,
  getImportError,
  getImporters,
  getImportResult,
  setFieldsValidationState,
  uploadImportData,
  uploadMiFleetImportData,
} from 'duxs/importer'
import { doesCurrentUserHaveAccessFromSetting, getLocale } from 'duxs/user'
import { getSettings } from 'duxs/user-sensitive-selectors'
import Icon from 'src/components/Icon'
import { IMPORT_SUPPORTED_EXTENSIONS } from 'src/modules/importer/components/FileSelector'
import {
  isFuelFraudByCostTab,
  isFuelFraudByLocationOnlyTab,
} from 'src/modules/mifleet/components/fraudValidation/shared/helpers'
import { getIsMFSettingsTab } from 'src/modules/mifleet/MFSettings/helpers'
import { exportCostsToXLSX } from 'src/modules/mifleet/operational/shared/utils'
import { Alert, Breadcrumbs, Button, SectionHeader, Spinner } from 'src/util-components'
import { ctIntl } from 'src/util-components/ctIntl'
import { exportJSONToXLSFile } from 'src/util-functions/file-utils'

import { minimalizeHeaders } from 'cartrack-utils'
import FieldMapper from './components/FieldMapper'
import FileSelector from './components/FileSelector'
import ImportSelector from './components/ImportSelector'
import PreviewFormat from './components/PreviewFormat'
import ResultView from './components/ResultView'
import UploadView from './components/UploadView'
import { mfImportKeysToSkipMiddleSteps } from './utils/mifleet-import-helpers'
import isValidMFPrompts from './utils/mifleet-prompt-validation'
import { ImportTemplateData } from './utils/mifleet-template-exporter'
import removeEmptyLines from './utils/remove-empty-import'
import translateMiFleetImportError from './utils/translate-mifleet-import-error'

const views = {
  upload: 0,
  mapper: 1,
  review: 2,
  result: 3,
}

const viewHeaders = ['Upload File', 'Match Found Fields', 'Review', 'Result']
class Import extends Component {
  static propTypes = {
    // State
    importers: arrayOf(shape({})).isRequired,
    categories: arrayOf(shape({})).isRequired,
    importResult: arrayOf(shape({})),
    importError: string,
    fileFieldsValid: bool,
    errorMessage: string,
    mifleetLiteDefaultImport: string,
    isSelectedMenu: bool,

    // Dispatch
    doFetchImporters: func.isRequired,
    doUploadImportData: func.isRequired,
    doUploadMiFleetImportData: func.isRequired,
    clearImportResult: func.isRequired,
    setFieldsValidationState: func.isRequired,

    // HOC
    history: shape({
      goBack: func,
    }),

    // ACL
    importSettings: shape({}).isRequired,
    selectedLocale: string,

    // Settings
    defaultCountry: string,

    // Overview Geo Location Fuelling
    handleOverviewFuellingBack: func,
  }

  static defaultProps = {
    importResult: null,
    importError: null,
    mifleetLiteDefaultImport: null,
    selectedLocale: '',
    defaultCountry: '',
  }

  state = {
    activeView: 0,
    filterHeader: [],
    makeEmptyFile: false, //REMOVE ATTACHED FILE
  }

  componentDidMount() {
    if (this.props.importers.length > 0) {
      this.getSelectedImporter(this.props)
    } else {
      this.props.doFetchImporters()
    }
  }

  componentDidUpdate(prevProps, prevState) {
    if (this.props.importers !== prevProps.importers) {
      this.getSelectedImporter(this.props)
    }
    //just will affect on Mifleet
    if (
      Boolean(this.state.makeEmptyFile) &&
      Boolean(this.state.makeEmptyFile) !== Boolean(prevState.makeEmptyFile)
    ) {
      this.setState({ makeEmptyFile: false })
    }
    if (
      Boolean(this.props.isSelectedMenu) &&
      Boolean(this.props.isSelectedMenu) !== prevProps.isSelectedMenu
    ) {
      this.handleClearFile()
      this.setState({ makeEmptyFile: true, selectedImporter: null })
    }
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    const checkMFSettings = getIsMFSettingsTab()
    const checkMFFuelFraudByCost = isFuelFraudByCostTab()
    const checkMFFuelFraudByLocationOnly = isFuelFraudByLocationOnlyTab()
    const { categories, importers } = nextProps
    let selectedCategory
    let selectedImporter

    if (
      (checkMFSettings || checkMFFuelFraudByCost || checkMFFuelFraudByLocationOnly) &&
      !prevState.selectedCategory
    ) {
      selectedCategory = find(categories, {
        name: 'MiFleet',
      })

      return {
        selectedCategory,
      }
    }

    if (checkMFFuelFraudByCost && !prevState.selectedImporter) {
      selectedImporter = find(importers, {
        mifleet_import_type: 'FUEL-GLOBAL',
      })

      return {
        selectedImporter,
      }
    } else if (checkMFFuelFraudByLocationOnly && !prevState.selectedImporter) {
      selectedImporter = find(importers, {
        mifleet_import_type: 'FUEL-GLOBAL',
      })

      return {
        selectedImporter,
      }
    }

    return null
  }

  getViewDescriptions = (view) => {
    const { selectedCategory } = this.state

    const isMiFleetCategory = selectedCategory?.name === 'MiFleet'

    const descriptions = [
      <Fragment key={'importData.uploadFile.description'}>
        {ctIntl.formatMessage(
          { id: 'importData.uploadFile.description' },
          {
            values: {
              supportedFiles: IMPORT_SUPPORTED_EXTENSIONS.map(
                (extension) => `.${extension}`,
              ).join(', '),
            },
          },
        )}
        {!isMiFleetCategory && (
          <StyledSecondDescription>
            {ctIntl.formatMessage({
              id: 'importData.uploadFile.second.description',
            })}
          </StyledSecondDescription>
        )}
      </Fragment>,
      ctIntl.formatMessage({
        id: 'We’ve analyzed your file and found the following column headings. Please match the required Fleet Field on the left to the appropriate column heading from your file',
      }),
      ctIntl.formatMessage({
        id: "Double check the sample rows below and click next when you're sure they are correct.",
      }),
    ]

    return descriptions[view] || ''
  }

  generateBackButtonIfNeeded = () =>
    // eslint-disable-next-line no-nested-ternary
    this.props.history && this.props.history.length > 0
      ? [
          {
            label: 'Back',
            onClick: () => this.props.history.goBack(),
          },
        ]
      : isFuelFraudByCostTab() || isFuelFraudByLocationOnlyTab()
        ? [
            {
              label: 'Back to List',
              onClick: () => this.props.handleOverviewFuellingBack('list'),
            },
          ]
        : []

  breadcrumbs = compact([
    ...this.generateBackButtonIfNeeded(),
    this.props.history
      ? {
          label: 'Import Data',
        }
      : null,
  ])

  getSelectedImporter(props) {
    const { categories, importers, location } = props

    let selectedCategory = null
    let selectedImporter = null
    const checkMFSettings = getIsMFSettingsTab()
    const checkMFFuelFraudByCost = isFuelFraudByCostTab()
    const checkMFFuelFraudByLocationOnly = isFuelFraudByLocationOnlyTab()

    if (checkMFSettings) {
      selectedCategory = find(categories, {
        name: 'MiFleet',
      })
    } else if (checkMFFuelFraudByCost || checkMFFuelFraudByLocationOnly) {
      selectedCategory = find(categories, {
        name: 'MiFleet',
      })
      selectedImporter = find(importers, {
        mifleet_import_type: checkMFFuelFraudByCost
          ? 'FUEL-GLOBAL'
          : 'FUEL-VALIDATION-GLOBAL',
      })
    } else if (location && location.state) {
      selectedCategory = find(categories, {
        name: location.state.selectedCategory,
      })
      selectedImporter = find(importers, {
        name: location.state.selectedImporter,
      })
    }

    this.setState({ selectedCategory, selectedImporter })
  }

  generateImportData = (prompts, fieldMap, data, isMiFleet) => {
    if (isMiFleet) {
      const newFieldMap = {}

      for (const [key, value] of Object.entries(fieldMap)) {
        if (key.split(':')[1] !== 'XXX') {
          let newKey = key
          if (isValidMFPrompts(key)) {
            newKey = key.split(':')[1]
          }
          newFieldMap[newKey] = value
        }
      }
      return data.map((d) => {
        const newObject = {}
        for (const [key, value] of Object.entries(newFieldMap)) {
          if (
            this.state.selectedImporter.mifleet_import_type ===
              'ZA-STANDARDBANK360-MULTICOST' &&
            key.includes('__')
          ) {
            const index = key.split('__')[1]
            newObject[key] = d[value][index - 1]
          } else {
            newObject[key] = d[value]
          }
        }
        return newObject
      })
    } else {
      return data.map((d) => prompts.map((h) => d[fieldMap[h]]))
    }
  }

  handleChangeFieldMap = (fieldMap) => this.setState({ fieldMap })

  handleClickBack = (isMiFleetCategory) => {
    if (this.state.activeView > 0) {
      if (
        isMiFleetCategory &&
        mfImportKeysToSkipMiddleSteps.includes(
          this.state.selectedImporter.mifleet_import_type,
        )
      ) {
        return this.setState({ activeView: 0 })
      }

      if (isMiFleetCategory && this.state.activeView === 2) {
        this.props.setFieldsValidationState({ fileFieldsValid: true })
      }
      this.setState({ activeView: this.state.activeView - 1 })
    } else if (isFuelFraudByCostTab() || isFuelFraudByLocationOnlyTab()) {
      this.props.handleOverviewFuellingBack('list')
    } else {
      this.props.history.goBack()
    }
  }

  getImportsFieldMapAndFilterHeader = () => {
    const { selectedImporter, headers, fileName } = this.state

    if (selectedImporter.is_mifleet) {
      if (fileName.split('.').pop().toLowerCase() === 'csv') {
        const miFleetCSVFieldMap = selectedImporter.prompts.reduce((acc, cur, i) => {
          if (selectedImporter.mifleet_import_type.includes('FUELLING-NZ-ZENERGY')) {
            const pointer = ctIntl.formatMessage({
              id: cur.split(':')[0].replace('*', ''),
            })

            const newHeader = headers.find(
              (header) =>
                header && minimalizeHeaders(pointer) === minimalizeHeaders(header),
            )

            acc[cur] = newHeader
          } else {
            acc[cur] = headers[i]
          }
          return acc
        }, {})
        return {
          newFieldMap: miFleetCSVFieldMap,
          filterHeader: selectedImporter.prompts,
        }
      }

      const parsedArray = [] //use for column name (excel file)
      const newHeaderState = [] //use for state

      Object.keys(this.state.parsedFile[0]).map((item) => {
        parsedArray.push(item)
      })

      const newFieldMap = selectedImporter.prompts.reduce((acc, cur) => {
        const pointer = mfImportKeysToSkipMiddleSteps.includes(
          selectedImporter.mifleet_import_type,
        )
          ? cur.split(':')[0]
          : ctIntl.formatMessage({ id: cur.split(':')[0].replace('*', '') })
        if (
          parsedArray.some((i) => minimalizeHeaders(pointer) === minimalizeHeaders(i))
        ) {
          newHeaderState.push(cur)
          const newHeader = headers.find(
            (header) =>
              header && minimalizeHeaders(pointer) === minimalizeHeaders(header),
          )
          acc[cur] = newHeader
        }

        return acc
      }, {})

      return { newFieldMap, filterHeader: newHeaderState }
    } else {
      const newFieldMap = selectedImporter.prompts.reduce((acc, cur, i) => {
        acc[cur] = headers[i]
        return acc
      }, {})

      return { newFieldMap, filterHeader: selectedImporter.prompts }
    }
  }

  handleClickNext = () => {
    const { selectedCategory, selectedImporter, activeView, parsedFile } = this.state

    const { newFieldMap, filterHeader } = this.getImportsFieldMapAndFilterHeader()

    const newImportData = this.generateImportData(
      filterHeader,
      newFieldMap,
      parsedFile,
      selectedImporter.is_mifleet,
    )

    if (
      selectedCategory?.name === 'MiFleet' &&
      mfImportKeysToSkipMiddleSteps.includes(selectedImporter.mifleet_import_type)
    ) {
      const importData = newImportData

      const newState = {
        activeView: 3,
        fieldMap: newFieldMap,
        importData,
        filterHeader,
      }
      this.setState(newState)

      return
    }

    const newState = { activeView: activeView + 1, filterHeader }

    if (activeView === views.upload) {
      newState.fieldMap = newFieldMap
    }

    if (activeView === views.mapper) {
      newState.importData = this.generateImportData(
        filterHeader,
        this.state.fieldMap,
        this.state.parsedFile,
        this.state.selectedImporter.is_mifleet,
      )
    }

    this.setState(newState)
  }

  handleFileChange = ({ parsed, headers, fileName }) => {
    const trimmedHeaders = (headers || []).filter((item) => item !== '')

    const mifleetImportType = this.state.selectedImporter.mifleet_import_type

    const isFileInvalid =
      isNil(trimmedHeaders) ||
      isEmpty(trimmedHeaders) ||
      trimmedHeaders.some((item) =>
        String(item).includes(';') ||
        String(item).includes(':') ||
        mifleetImportType === 'MULTICOST-ZA-SHELL' ||
        mifleetImportType === 'TOLL-SANRAL-ZA'
          ? ''
          : isEmpty(String(item)),
      )

    this.props.setFieldsValidationState({ fileFieldsValid: true })

    this.setState({
      parsedFile: parsed,
      headers: trimmedHeaders,
      fileName,
      isFileInvalid,
    })
  }

  handleClearFile = () => {
    this.setState({
      parsedFile: null,
      headers: null,
      fileName: null,
      isFileInvalid: false,
    })
  }

  handleImport = () => {
    const { importData, selectedImporter } = this.state

    if (selectedImporter.is_mifleet) {
      this.props.doUploadMiFleetImportData({
        data: removeEmptyLines(importData),
        prompts: selectedImporter.stringPrompts,
        mifleet_import_type: selectedImporter.mifleet_import_type,
      })
    } else {
      this.props.doUploadImportData({
        data: importData,
        id: selectedImporter.id,
        name: selectedImporter.name,
      })
    }
  }

  handleImporterChange = (importUpdates) => {
    this.setState(importUpdates)
    if (this.state.selectedCategory?.name === 'MiFleet') {
      this.handleClearFile()
      this.setState({ makeEmptyFile: true })
    }
  }

  handleRestart = () => {
    const { importers } = this.props

    const checkMFFuelFraudByCost = isFuelFraudByCostTab()
    const checkMFFuelFraudByLocationOnly = isFuelFraudByLocationOnlyTab()
    let selectedImporter = null

    if (checkMFFuelFraudByLocationOnly || checkMFFuelFraudByCost) {
      selectedImporter = 'FUEL-GLOBAL'
    }

    this.setState({
      activeView: views.upload,
      selectedImporter: selectedImporter
        ? find(importers, {
            mifleet_import_type: selectedImporter,
          })
        : null,
      parsedFile: null,
      headers: null,
      fileName: null,
    })
  }

  exportFile = () => {
    const {
      state: {
        selectedImporter: { is_mifleet },
        importData,
        fileName,
      },
      props: { importResult },
    } = this

    let indexId = -1
    const headers = [
      ctIntl.formatMessage({ id: 'Result' }),
      ...this.state.filterHeader,
    ].reduce((acc, value) => {
      const data = acc

      if (value.split(':')[1] !== 'XXX') {
        indexId = indexId + 1
        data[indexId] =
          is_mifleet && isValidMFPrompts(value) ? value.split(':')[0] : value
      }
      return data
    }, {})

    const specificResult = is_mifleet
      ? importResult.objectList.importData.map((d) => ({
          message: d.error_message || 'Success',
          status: d.import_status,
        }))
      : importResult

    const cleanImportData = removeEmptyLines(importData)

    const importedData = is_mifleet
      ? cleanImportData.map((d) => Object.values(d))
      : importData

    const columnData = zipWith(specificResult, importedData, concat)

    const formattedData = columnData.reduce((acc, value) => {
      const data = acc
      const reducedValue = value.reduce((a, b, index) => {
        const rowkey = a
        let rowValue = b

        if (b && has(b, 'status')) {
          rowValue = b.message
        }

        if (is_mifleet && headers[index] === ctIntl.formatMessage({ id: 'Result' })) {
          rowValue = translateMiFleetImportError(rowValue)
        }

        rowkey[index] = rowValue
        return rowkey
      }, {})
      data.push(reducedValue)
      return data
    }, [])

    return exportJSONToXLSFile(headers, formattedData, `Result ${fileName}`)
  }

  generateGuideName = () => {
    const { selectedLocale } = this.props

    return `/assets/mfImportsGuide${selectedLocale === 'pt-PT' ? '-PT' : ''}.pdf`
  }

  filterMiFleetImporter = (stringToFilter) =>
    this.props.importers.filter(
      (imp) =>
        imp.mifleet_import_type &&
        imp.mifleet_import_type.toLowerCase().includes(stringToFilter),
    )

  getFilteredImporters = () => {
    const { mifleetLiteDefaultImport, importers } = this.props

    if (getIsMFSettingsTab() && mifleetLiteDefaultImport) {
      switch (mifleetLiteDefaultImport) {
        case 'fuel': {
          return this.filterMiFleetImporter('fuel')
        }
        case 'tolls': {
          return this.filterMiFleetImporter('toll')
        }
        case 'fines': {
          return this.filterMiFleetImporter('fine')
        }
        case 'maintenance': {
          return this.filterMiFleetImporter('maintenance')
        }
        case 'accidents': {
          return this.filterMiFleetImporter('accident')
        }
        case 'multiImport': {
          return this.filterMiFleetImporter('multicost')
        }
        default: {
          return importers
        }
      }
    } else {
      return importers
    }
  }

  render() {
    const {
      categories,
      importers,
      importSettings,
      clearImportResult,
      fileFieldsValid,
      errorMessage,
      importResult,
      importError,
      defaultCountry,
      isSelectedMenu,
    } = this.props

    const {
      activeView,
      parsedFile,
      selectedImporter,
      selectedCategory,
      headers,
      fieldMap,
      importData,
      isFileInvalid = false,
    } = this.state

    const isMiFleetCategory = selectedCategory?.name === 'MiFleet'

    const mfLengthValidator =
      isMiFleetCategory &&
      activeView === views.review &&
      (importData || []).length > 1000 &&
      !mfImportKeysToSkipMiddleSteps.includes(
        (selectedImporter || {}).mifleet_import_type,
      )

    const isNextDisabled =
      (activeView === views.upload && (!parsedFile || !selectedImporter)) ||
      isFileInvalid ||
      !fileFieldsValid ||
      mfLengthValidator

    const isDownloadDisabled = !importResult

    const isResult = activeView === views.result

    return (
      <div className="Importer">
        <header className="Importer-header">
          <Breadcrumbs
            data={this.breadcrumbs}
            className="util-hideXsDown"
          />
          <div className="Importer-navigationButtons">
            <Button
              label={activeView === views.upload ? 'Cancel' : 'Back'}
              onClick={() => this.handleClickBack(isMiFleetCategory)}
              grouped
              disabled={getIsMFSettingsTab() && activeView === views.upload}
            />
            <Button
              label={isResult ? 'New Upload' : 'Next'}
              disabled={isNextDisabled}
              onClick={isResult ? this.handleRestart : this.handleClickNext}
              grouped
              action
            />
            {activeView === views.result && (
              <Button
                label="Download"
                onClick={this.exportFile}
                grouped
                disabled={isDownloadDisabled}
                action
              />
            )}
          </div>
        </header>
        {importers ? (
          <section className="Importer-main">
            <Alert
              show={isFileInvalid || (activeView !== views.upload && !fileFieldsValid)}
              message={
                isMiFleetCategory && errorMessage
                  ? errorMessage
                  : ctIntl.formatMessage({
                      id: 'This file is not valid. Please check your file.',
                    })
              }
            />
            <SectionHeader label={viewHeaders[activeView]} />
            <div className="Importer-description col">
              {this.getViewDescriptions(activeView)}
              {mfLengthValidator && (
                <StyledWarningDescription>
                  <Icon icon="exclamation-triangle" />
                  <span>
                    {ctIntl.formatMessage({
                      id: 'imports.mifleet.uploaded.warning',
                    })}
                  </span>
                </StyledWarningDescription>
              )}
            </div>

            {activeView === views.upload && (
              <ImportSelector
                categories={categories}
                importers={this.getFilteredImporters()}
                selectedImporter={selectedImporter}
                selectedCategory={selectedCategory}
                onChange={this.handleImporterChange}
                acl={importSettings}
                defaultCountry={defaultCountry}
                quickImportMF={mfImportKeysToSkipMiddleSteps}
                selectedMenu={this.props.mifleetLiteDefaultImport}
              />
            )}
            {activeView === views.upload && (
              <StyledFileSelector
                onChange={this.handleFileChange}
                onClearFile={this.handleClearFile}
                allowClipBoardData={!isMiFleetCategory}
                isMiFleetCategory={isMiFleetCategory}
                makeEmptyFile={this.state.makeEmptyFile}
                selectedImporter={selectedImporter}
                disabled={Boolean(!selectedImporter)}
                supportedExtensions={IMPORT_SUPPORTED_EXTENSIONS}
              >
                {selectedImporter?.template &&
                  !mfImportKeysToSkipMiddleSteps.includes(
                    selectedImporter.mifleet_import_type,
                  ) && (
                    <PreviewFormatButton>
                      {selectedImporter.is_mifleet ? (
                        <Button
                          label="import.downloadTemplate.button"
                          onClick={() =>
                            exportCostsToXLSX(
                              ImportTemplateData[selectedImporter.mifleet_import_type]
                                .headers,
                              ImportTemplateData[selectedImporter.mifleet_import_type]
                                .data,
                              {
                                fileName: camelCase(
                                  ctIntl.formatMessage({
                                    id: selectedImporter.name,
                                  }),
                                ),
                              },
                            )
                          }
                        />
                      ) : (
                        <a
                          className="Button"
                          href={`/assets/${selectedImporter.template}`}
                          download
                        >
                          {ctIntl.formatMessage({
                            id: 'import.downloadTemplate.button',
                          })}
                        </a>
                      )}
                    </PreviewFormatButton>
                  )}
                {isMiFleetCategory && (
                  <PreviewFormatButton>
                    <a
                      className="Button"
                      href={this.generateGuideName()}
                      download
                    >
                      {ctIntl.formatMessage({
                        id: 'import.downloadMifleetImports.button',
                      })}
                    </a>
                  </PreviewFormatButton>
                )}
              </StyledFileSelector>
            )}

            {!isMiFleetCategory && activeView === views.upload && selectedImporter && (
              <PreviewFormat selectedImporter={selectedImporter} />
            )}

            {activeView === views.mapper && (
              <FieldMapper
                fields={selectedImporter && this.state.filterHeader}
                userFields={headers}
                fieldMap={fieldMap}
                onChange={this.handleChangeFieldMap}
                isMiFleet={selectedImporter.is_mifleet}
                isSelectedMenu={isSelectedMenu}
                onRestart={this.handleRestart}
                selectedImporter={selectedImporter.mifleet_import_type}
              />
            )}
            {activeView === views.review && (
              <UploadView
                headers={this.state.filterHeader}
                importData={importData}
                isMiFleet={selectedImporter.is_mifleet}
                isSelectedMenu={isSelectedMenu}
                onRestart={this.handleRestart}
              />
            )}
            {activeView === views.result && (
              <ResultView
                onMount={this.handleImport}
                headers={this.state.filterHeader}
                importData={importData}
                result={importResult}
                clearImportResult={clearImportResult}
                isMiFleet={selectedImporter.is_mifleet}
                importError={importError}
                isSelectedMenu={isSelectedMenu}
                onRestart={this.handleRestart}
              />
            )}
            {isMiFleetCategory &&
              activeView === views.upload &&
              !mfImportKeysToSkipMiddleSteps.includes(
                (selectedImporter || {}).mifleet_import_type,
              ) && (
                <StyledWarningDescription>
                  <Icon icon="exclamation-triangle" />
                  <span>
                    {ctIntl.formatMessage({
                      id: 'imports.mifleet.upload.warning',
                    })}
                  </span>
                </StyledWarningDescription>
              )}
          </section>
        ) : (
          <Spinner />
        )}
      </div>
    )
  }
}

function mapStateToProps(state) {
  // Get all the import related ACL
  const settings = getSettings(state)
  const importSettings = Object.keys(settings)
    .filter(
      (setting) =>
        setting.includes('Import') && setting !== 'userImportData' && settings[setting],
    )
    .reduce((acc, key) => ({ ...acc, [key]: settings[key] }), {})

  const isMiFleetUser = doesCurrentUserHaveAccessFromSetting(state, 'costs')

  // exclude 'communicator' in categories for fleet import settings
  const categories = getCategories(state).filter(
    (field) =>
      field.name !== 'Communicator' &&
      (!isMiFleetUser ? field.name !== 'MiFleet' : true),
  )

  const defaultCountry = settings.defaultCountry

  return {
    importers: getImporters(state),
    categories,
    importResult: getImportResult(state),
    importError: getImportError(state),
    importSettings,
    fileFieldsValid: getFileFieldsValid(state).fileFieldsValid,
    errorMessage: getFileFieldsValid(state).errorMessage,
    selectedLocale: getLocale(state),
    defaultCountry,
  }
}

const actionCreators = {
  doFetchImporters: fetchImporters,
  doUploadImportData: uploadImportData,
  doUploadMiFleetImportData: uploadMiFleetImportData,
  clearImportResult,
  setFieldsValidationState,
}

export default connect(mapStateToProps, actionCreators)(withRouter(Import))

const PreviewFormatButton = styled.div.attrs({
  className: 'PreviewFormat-button',
})`
  a.Button {
    display: unset;
  }

  text-align: unset;
`

const StyledFileSelector = styled(FileSelector)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .FileInput {
    margin: 15px 0;
  }
`
const StyledSecondDescription = styled.p`
  margin: 0;
`
const StyledWarningDescription = styled.div`
  color: red;

  span {
    margin-left: 10px;
  }
`
